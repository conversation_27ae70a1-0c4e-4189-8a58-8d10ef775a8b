// Jenkinsfile to build ALL microservices for multiple environments
pipeline {
    agent {
        docker {
            image 'maven:3.9-eclipse-temurin-21'
            args '--user root'
        }
    }

    parameters {
        choice(name: 'TARGET_ENVIRONMENT', choices: ['DEV', 'QA'], description: 'Select the environment to deploy to')
    }

    environment {
        SERVICES_LIST = 'ehr-backend-server ehr_abhaa_service ehr_auth_service ehr_twilo_service'
    }

    stages {
        stage('Checkout Code') {
            steps {
                checkout scm
            }
        }

        // --- THIS IS THE NEW STAGE ---
        // It runs once, before the parallel stages, to prepare the environment.
        stage('Prepare Build Environment') {
            steps {
                echo "Installing required tools..."
                sh 'apt-get update && apt-get install -y sshpass'
            }
        }

        stage('Build and Deploy All Services') {
            steps {
                script {
                    def deployServerIp = ''
                    def deployCredentialId = ''
                    def deployUser = ''

                    if (params.TARGET_ENVIRONMENT == 'DEV') {
                        deployServerIp = '*************'
                        deployCredentialId = 'dev-server-password'
                        deployUser = 'devuser'
                    } else if (params.TARGET_ENVIRONMENT == 'QA') {
                        deployServerIp = '************'
                        deployCredentialId = 'ec2-password-credential'
                        deployUser = 'testuser'
                    }

                    def parallelStages = [:]
                    def services = env.SERVICES_LIST.split(' ')

                    for (service in services) {
                        def currentService = service

                        parallelStages["Deploy ${currentService}"] = {
                            dir(currentService) {
                                echo "--> Building ${currentService}..."
                                sh 'mvn clean package -Dquarkus.package.type=uber-jar -DskipTests'

                                echo "--> Deploying ${currentService} to ${params.TARGET_ENVIRONMENT}..."
                                // The 'apt-get' command has been removed from here.

                                withCredentials([usernamePassword(credentialsId: deployCredentialId, usernameVariable: 'SSH_USERNAME', passwordVariable: 'SSH_PASSWORD')]) {
                                    sh """
                                        export SSHPASS="\${SSH_PASSWORD}"
                                        sshpass -e scp -o StrictHostKeyChecking=no target/*-runner.jar ${deployUser}@${deployServerIp}:/opt/apps/
                                        sshpass -e ssh -o StrictHostKeyChecking=no ${deployUser}@${deployServerIp} 'mv /opt/apps/*-runner.jar /opt/apps/${currentService}.jar && sudo systemctl restart ${currentService}'
                                    """
                                }
                            }
                        }
                    }
                    parallel parallelStages
                }
            }
        }
    }
}