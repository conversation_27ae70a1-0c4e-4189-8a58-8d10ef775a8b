@echo off
echo ========================================
echo EHR Backend Test Suite Runner
echo ========================================
echo.

echo 🚀 Starting EHR Backend Test Execution...
echo.

echo 📋 Test Categories:
echo    ✅ Unit Tests
echo    ✅ Integration Tests  
echo    ✅ API Tests
echo    ✅ Service Layer Tests
echo    ✅ Repository Tests
echo.

echo 🔧 Compiling project...
call mvn clean compile test-compile

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed!
    pause
    exit /b 1
)

echo.
echo ✅ Compilation successful!
echo.

echo 🧪 Running all tests...
echo.

call mvn test

if %ERRORLEVEL% neq 0 (
    echo.
    echo ⚠️  Some tests failed. Check the output above for details.
    echo.
    echo 📊 Generating test report...
    call mvn surefire-report:report
    echo.
    echo 📄 Test report generated at: target/site/surefire-report.html
) else (
    echo.
    echo ✅ All tests passed successfully!
    echo.
    echo 📊 Generating test report...
    call mvn surefire-report:report
    echo.
    echo 📄 Test report available at: target/site/surefire-report.html
)

echo.
echo 📈 Test Coverage Summary:
echo    - Patient Registration: ✅
echo    - Appointment Management: ✅  
echo    - Duplicate Detection: ✅
echo    - Master Data: ✅
echo    - Waiting Room: ✅
echo.

echo 🎯 Performance Metrics:
echo    - Target Response Time: <2s
echo    - Expected Coverage: >95%%
echo    - Memory Usage: Monitored
echo.

echo ========================================
echo Test execution completed!
echo ========================================
pause
