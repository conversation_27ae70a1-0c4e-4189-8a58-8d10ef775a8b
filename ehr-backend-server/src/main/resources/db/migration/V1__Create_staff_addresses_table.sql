-- Migration to create staff_addresses table for multiple addresses per staff member
-- This replaces the embedded address fields in facility_staff table

-- Create staff_addresses table
CREATE TABLE IF NOT EXISTS staff_addresses (
    address_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    staff_id UUID NOT NULL,
    address_line_1 VARCHAR(255),
    address_line_2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'India',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add foreign key constraint to facility_staff table
ALTER TABLE staff_addresses 
ADD CONSTRAINT fk_staff_addresses_staff_id 
FOREIGN KEY (staff_id) REFERENCES facility_staff(staff_id) ON DELETE CASCADE;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_staff_addresses_staff_id ON staff_addresses(staff_id);

-- If there are existing embedded address fields in facility_staff, 
-- we would migrate them here, but since we're starting fresh, 
-- we'll just ensure the table structure is correct

-- Remove any embedded address columns from facility_staff if they exist
-- (This is safe to run even if columns don't exist)
DO $$
BEGIN
    -- Remove embedded address columns if they exist
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'facility_staff' AND column_name = 'address_line_1') THEN
        ALTER TABLE facility_staff DROP COLUMN address_line_1;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'facility_staff' AND column_name = 'address_line_2') THEN
        ALTER TABLE facility_staff DROP COLUMN address_line_2;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'facility_staff' AND column_name = 'city') THEN
        ALTER TABLE facility_staff DROP COLUMN city;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'facility_staff' AND column_name = 'state') THEN
        ALTER TABLE facility_staff DROP COLUMN state;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'facility_staff' AND column_name = 'postal_code') THEN
        ALTER TABLE facility_staff DROP COLUMN postal_code;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'facility_staff' AND column_name = 'country') THEN
        ALTER TABLE facility_staff DROP COLUMN country;
    END IF;
END $$;
