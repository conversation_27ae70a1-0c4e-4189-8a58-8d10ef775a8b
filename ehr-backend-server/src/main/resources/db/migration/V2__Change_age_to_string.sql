-- Migration to change age column from integer to string in facility_staff table
-- This allows for more flexible age representation (e.g., "25 years", "2 months", etc.)

-- For PostgreSQL, we need to handle the type conversion carefully
-- First, add a new temporary column
ALTER TABLE facility_staff ADD COLUMN age_temp VARCHAR(10);

-- Copy existing age values to the new column, converting integer to string
UPDATE facility_staff SET age_temp = CAST(age AS VARCHAR(10));

-- Drop the old age column
ALTER TABLE facility_staff DROP COLUMN age;

-- Rename the temporary column to age
ALTER TABLE facility_staff RENAME COLUMN age_temp TO age;

-- Add NOT NULL constraint to the new age column
ALTER TABLE facility_staff ALTER COLUMN age SET NOT NULL;

-- Set default value for new records
ALTER TABLE facility_staff ALTER COLUMN age SET DEFAULT '0';

-- For H2 database (development), the syntax is slightly different
-- This migration will work for both PostgreSQL and H2
