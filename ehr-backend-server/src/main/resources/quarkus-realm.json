{"id": "********-8ffa-4a8a-9881-2b7eeb84f800", "realm": "quarkus", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "3fc80564-13ac-4e7b-9986-322f571e82bc", "name": "confidential", "composite": false, "clientRole": false, "containerId": "********-8ffa-4a8a-9881-2b7eeb84f800", "attributes": {}}, {"id": "8c1abe12-62fe-4a06-ae0d-f5fb67dddbb0", "name": "admin", "composite": false, "clientRole": false, "containerId": "********-8ffa-4a8a-9881-2b7eeb84f800", "attributes": {}}, {"id": "5afce544-6a3c-495f-b805-fd737cf5081e", "name": "user", "composite": false, "clientRole": false, "containerId": "********-8ffa-4a8a-9881-2b7eeb84f800", "attributes": {}}, {"id": "2b6632ed-9e3b-4e28-86ed-1d0239c7cefc", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "********-8ffa-4a8a-9881-2b7eeb84f800", "attributes": {}}, {"id": "d70f9032-c77a-4aca-88d8-d914ee2905a4", "name": "default-roles-quarkus", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["manage-account", "view-profile"]}}, "clientRole": false, "containerId": "********-8ffa-4a8a-9881-2b7eeb84f800", "attributes": {}}, {"id": "5dbf8bd3-db6f-4394-94f9-b311d08baf4c", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "********-8ffa-4a8a-9881-2b7eeb84f800", "attributes": {}}], "client": {"realm-management": [{"id": "6c2447e9-a8ca-402b-953c-************", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "d321086a-5816-404d-bcc7-7984dd83d36b", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "6073c9f6-b1c2-4680-9d28-ab1254618b7b", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "e2f20b42-23d3-4e88-a2ef-1bfeb6716020", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "f07629cf-203d-4beb-8dbf-ec8aa1f035b3", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "832b6db4-d305-4141-b56c-6e93a78a3e68", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "bfc8e0e6-341f-4355-bebb-da64f28ff13f", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["query-clients", "view-identity-providers", "manage-authorization", "view-users", "impersonation", "create-client", "manage-events", "query-groups", "view-authorization", "query-users", "manage-realm", "manage-identity-providers", "view-realm", "view-clients", "manage-clients", "query-realms", "view-events", "manage-users"]}}, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "894aaa75-29f9-452c-9ea3-429a51404575", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "d6e53477-0d1f-46f6-ace8-0b81e658196a", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "d7141f22-ae51-489e-861c-9cb12f943fec", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "692bf834-049f-4f15-95cf-7b40cadf4272", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "1cc2c76c-6b2f-47d0-bf0a-e260247f2e73", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "a3f00bf1-2b0e-4127-bbd7-f2c8f9005ea9", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "349db29b-1e3f-4e94-91a5-3afb1f1ce789", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "e454c0c4-846e-4af3-8f27-2c864e30517f", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "d085f668-dae1-4908-b755-39b223c16d29", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "ba4367d7-94bb-43d8-b175-7842dbfe3102", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "632628d0-f783-4114-97b2-2622a76e5674", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}, {"id": "7ec35cf2-029b-47ac-a570-e32dfb4821e8", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "backend-service": [{"id": "df147a91-6da7-4bbc-866c-f30cf99b2637", "name": "uma_protection", "composite": false, "clientRole": true, "containerId": "0ac5df91-e044-4051-bd03-106a3a5fb9cc", "attributes": {}}], "account-console": [], "broker": [{"id": "fa00b5ce-10f0-4643-91f4-4092121a55ec", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "371a2b90-ebb3-4255-9d2e-bc3949d5bd2a", "attributes": {}}], "account": [{"id": "1b242c4a-3141-40c5-bd67-66e6c21ad91b", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "f362cd79-8596-407b-9e01-7c999e43d488", "attributes": {}}, {"id": "4943cb69-5dc1-42ba-a7b4-60b841b49a92", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "f362cd79-8596-407b-9e01-7c999e43d488", "attributes": {}}, {"id": "b910b7e5-7f8f-4c7f-ab9a-27a9df77e062", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "f362cd79-8596-407b-9e01-7c999e43d488", "attributes": {}}, {"id": "db73da06-3099-4b11-97c6-13c403dab0ae", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "f362cd79-8596-407b-9e01-7c999e43d488", "attributes": {}}, {"id": "2d40ac4f-48be-47f9-bf1f-458354158de9", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "f362cd79-8596-407b-9e01-7c999e43d488", "attributes": {}}, {"id": "2713bb5d-2760-44f4-a276-28bcf6bdbe8e", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "f362cd79-8596-407b-9e01-7c999e43d488", "attributes": {}}, {"id": "228b74ca-9239-4fdc-a81c-59f324a9e4e8", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "f362cd79-8596-407b-9e01-7c999e43d488", "attributes": {}}]}}, "groups": [], "defaultRole": {"id": "d70f9032-c77a-4aca-88d8-d914ee2905a4", "name": "default-roles-quarkus", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "********-8ffa-4a8a-9881-2b7eeb84f800"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "f362cd79-8596-407b-9e01-7c999e43d488", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/quarkus/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/quarkus/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "1148f171-a813-463d-bc3d-61b47c300a31", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/quarkus/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/quarkus/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "32fb140b-17b0-4c5c-9138-d9a9bc2d3eb1", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "16fb473f-45e3-40bb-81bf-4c7f69b92ae2", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "371a2b90-ebb3-4255-9d2e-bc3949d5bd2a", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "0ac5df91-e044-4051-bd03-106a3a5fb9cc", "clientId": "backend-service", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "secret", "redirectUris": ["*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "3eac903f-c16b-4a78-a7e8-eb8f4d402b71", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}, {"id": "8422cefe-7f42-4f3b-abad-5f06f7d4b748", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "988e47d6-2055-45eb-82d6-0b8b25c629fc", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "authorizationSettings": {"allowRemoteResourceManagement": true, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "User Resource", "ownerManagedAccess": false, "attributes": {}, "_id": "df1b74a9-3f10-499d-a581-368de48e512b", "uris": ["/api/users/*"]}, {"name": "Administration Resource", "ownerManagedAccess": false, "attributes": {}, "_id": "7124e2f1-e6dc-44b4-87ab-24b010090b97", "uris": ["/api/admin/*"]}], "policies": [{"id": "b8710fa6-160e-4de0-adf3-398c7007a0af", "name": "Any User Policy", "description": "Any user granted with the user role can access something", "type": "role", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"roles": "[{\"id\":\"user\",\"required\":false}]"}}, {"id": "fcef30b2-68b2-4b78-9f3d-9162c6cdf5cb", "name": "Only Administrators", "description": "Only administrators can access", "type": "role", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"roles": "[{\"id\":\"admin\",\"required\":false}]"}}, {"id": "3479dd56-02e9-4222-94fe-6a13cd065195", "name": "User Resource Permission", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"User Resource\"]", "applyPolicies": "[\"Any User Policy\"]"}}, {"id": "60188298-d55b-4066-b231-6a7c56ff7cc5", "name": "Administration Resource Permission", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"Administration Resource\"]", "applyPolicies": "[\"Only Administrators\"]"}}], "scopes": [], "decisionStrategy": "UNANIMOUS"}}, {"id": "911f7ea1-5e42-4fdd-a0d9-767c62cbc41f", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "36f4981a-8b47-45ec-a24c-0d6832dce7a6", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/quarkus/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/quarkus/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "5ae0eba8-8f29-4048-8b57-b5b3878df79c", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "e17e3fdf-518f-44d8-9a8e-5743fe656c26", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "e12d0fe6-2aa8-4809-a8f5-69fc5d41ba0d", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "005aa9ba-7a81-49be-b14f-fecbbfc22111", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}, {"id": "c0c00c6d-85fc-40ed-b15a-693825b9f3a8", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "9c4af8e8-d303-4520-ae81-f87190c2bbc6", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "03a3006b-c808-4b19-a5ec-b01a14b3b00e", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "e606f224-24c8-46e3-ac5a-01e7fadb1562", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}]}, {"id": "74626818-9744-416e-ae77-d13be8940fee", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "13e76b05-7d24-43f4-ad4e-1d7655853512", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "0adad4c2-dbea-4105-a0c0-a6062c60a3f6", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "bd050b86-7be1-42a0-b2f6-b681183e560a", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "cc7f1c4e-c15b-425a-b2c4-c21e180f882f", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}, {"id": "47595a41-97d2-48b9-84e9-f66d9820ea9d", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "9150603e-ebc9-467e-a254-72d910622fa7", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "4f028250-4036-498d-894a-d356d251110a", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "e7401ec2-c877-4522-b390-d7ac13abfd22", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "90848608-3013-47dc-b6bd-e7d16e150cdd", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "dad89a6f-c6d3-485c-87c0-79bf304310dd", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "8070e658-3ed6-444c-b117-372b0d3e76bc", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "861faee7-38c1-4040-9bae-a1f9d24c4f60", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "7b1b5c7e-45ff-4052-b937-73b5a3689498", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "ef20556e-624d-4c08-9aac-3e8ef9cb5c57", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "03a03973-8d8d-495a-99af-f483e31eafcb", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "a64f2a47-50b6-4c55-9d0f-b5971ae63992", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "75f1df3a-0843-4786-ab2b-49c3b719eb92", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "1b582634-b01b-4900-a7d4-9ee95fb4012c", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long"}}, {"id": "0c4f4ea2-2d31-4ab1-bf97-e46344ff4336", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "55b83e07-ed5b-4441-980e-01b9234db5fe", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "e8e2523e-5044-47d4-863f-50b46e32307b", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "233f533d-40a4-4df2-b5a7-4536c8fdc3b8", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "ec6d80f6-3093-4b8f-b4a4-97b0ce8567b6", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "aae53b7f-83fb-4aff-85f4-93fc5f69b213", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}]}, {"id": "08d32803-c052-4b0a-98c4-538e627857b3", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "8cd9bda9-eff5-42e7-8e62-b9d2e0f37416", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "3bf71763-9a1f-413c-b52d-bf99f611a211", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "1a4f1401-2b80-47c9-922a-80f0d1537507", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "e80cfa11-e49b-46e5-a61d-633cc0e27e07", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins", "acr"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "486bf667-5362-4333-82e8-6057d5b3c37a", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "c4b992b7-5969-4dab-845b-87b7f876f571", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "6844e33b-6421-4633-a808-d06856bda363", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-full-name-mapper", "saml-user-property-mapper", "oidc-address-mapper", "saml-user-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "saml-role-list-mapper"]}}, {"id": "8b7dd0da-a538-49db-b120-5386fc3846cf", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "d5a9a762-12a0-445f-b460-511d04e11445", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "cb11bd78-941b-4596-99e5-5ce4a41d6dc1", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-full-name-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-property-mapper", "saml-user-property-mapper", "oidc-usermodel-attribute-mapper", "oidc-address-mapper"]}}, {"id": "174400f3-6a13-4818-99a2-cf0f1a5ece0f", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "d4455215-c88c-47a5-8527-9c21fef02a98", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "620e8030-7114-450c-b188-f2307fa9eaac", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["6b97cca4-f3d2-41f6-a225-ba846090df11"], "secret": ["7cJw81x86C-kGsDRw3mJzd_M6pPO3fecUlXlL1TYRUlUR7lK6hngmkxK05LiG6swheQIZYs6UkTM1nLORncEJA"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "e4432af5-5db8-4e30-8a0d-150a5ed675b5", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAwVW0ULkeQ4JidUfS6+AWndVr9YqFd5zIO8iM0UXfyOesPfvwccmdJdSVTtEJTe99TujfUARRRdl3uIXr+q0E+mQyVWf0v+nzFfcQpVYq29XtfwXQK0JzvGUbWki+JnCcvXvSRnc/fjLy20psaupBT6YxYyIcvHi1JCaA7dk5iyi6FPLFkJrF0i605z+PfeWfdm07uZl4ZZ9D/+dKM4V7dnYwg8kB3x32rcIevujGs621a5ZYyl8QzJM9LMyehe3t3whNX/86vJxpg63YZyk7uhZRBvSXHZWru6Je+PEN+9rq5GYPZjg1GhCf9ty5YQ1rihAKInE1hmGIkPSUqAfXbQIDAQABAoIBABWmzvxRVwHResxOoGD5Jh9D9QS5ICxemxAS7C5NqHu78k64xdtAbIB9l8ENaQDY5swtCUFRUnbDnYRFP0HCGbIfAYIOE9tgTHBzzGHvzfnzsdbNZFxL42FAY3lPdzgf2XXhBZINrcco1ojXg1/P30OXxaUn/1beH88uHrdEYgzi719VRuoB73gyIF9P7jrl1n8UTE6wlPAOi5ZD2ERRM2bs2AW2v+dNB38CmhkWqpvjWkEEQl7JSaFDxm9mkdUopL2CPKTOM6YW3k/IVU9nMuRy1/xKDZyPO1AvNqzfMqTqDRVphAJRL+EIY6NAa6uv32g0AT1zCc3FrOuoDRvhOwMCgYEA6GkNOtu8+qqnTUdgBWUnmbcpTgw0g7tBjEzZcI4KpZfXt8IdnNBnuOtI7/Gv3fhSP9yyp60Kzl/M+JwzxbDYNuQZh9ETMFOUrdtdcyIftanttf/HhXv8yA15My3gZXSo4Zk5R15KfEAUPQyYbIY3QZkl/f3koenl2kc6/5Ncps8CgYEA1PVQNoOoAoEG7D1PU91W9NnMby+xCp4SUejS2ZajveKoEMKwYOM4Gw2Mczql+KKYDs6UU7ACrbsMSC+qgWX1b+PTXkuXkEBOt4BjnHDzVHEHZwAPKbY3dQttqZEQgDQEkhNvANseY9MNipGH2PsC+yWwvfGmXIIClsjuselIrQMCgYA0PHhWxFDDXlvgCKzSSZwO9pX4DtI20X24FQXCQIqwEWLQ0HZraoQFkgyAsae/kjKaW/ZjrYcCP5IIRsN0KX1PSRFb72gKsp52PRnR6NeLszfUcjrcC7QTGsQk9qi65O+YVSIEonLHXneuCQixS7FbsxpQ7iHXKoUY/G2MSAjJDQKBgHCHJ9mGfBLGpyfRxcck4DwFklb2NnDL0hZiFnhPdzpxOCmTFqw7pHv3Ds4coXLeR8cbNZl7Gg8/FWBXf5tcivKSyi+7dd0NfgrM1tXlgU6PYz/z8g8yb99zKIE/XB7CXd/qUO982XQaI7+i4suu54ZaBih4NlYPhNq0VhzAjlyNAoGBAKb3SZqb3wHSE+cR8fDP8hs3v7HPhPbPIpdFyzV3RKvW+3cLtSABImwcxPbk/P0CoFWkIq9kxYOVLSJNFA5ptWuG49kTpJFaYiPDu3UHOKGl2tmPLjOLp6aBVvnQb+1rfLYtNxyK8tneBaG0lSoamqoGgXuhiEeoCjgVxkSUA9dd"], "keyUse": ["ENC"], "certificate": ["MIICnTCCAYUCBgGCxUsqLDANBgkqhkiG9w0BAQsFADASMRAwDgYDVQQDDAdxdWFya3VzMB4XDTIyMDgyMjExMjE1M1oXDTMyMDgyMjExMjMzM1owEjEQMA4GA1UEAwwHcXVhcmt1czCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMFVtFC5HkOCYnVH0uvgFp3Va/WKhXecyDvIjNFF38jnrD378HHJnSXUlU7RCU3vfU7o31AEUUXZd7iF6/qtBPpkMlVn9L/p8xX3EKVWKtvV7X8F0CtCc7xlG1pIviZwnL170kZ3P34y8ttKbGrqQU+mMWMiHLx4tSQmgO3ZOYsouhTyxZCaxdIutOc/j33ln3ZtO7mZeGWfQ//nSjOFe3Z2MIPJAd8d9q3CHr7oxrOttWuWWMpfEMyTPSzMnoXt7d8ITV//OrycaYOt2GcpO7oWUQb0lx2Vq7uiXvjxDfva6uRmD2Y4NRoQn/bcuWENa4oQCiJxNYZhiJD0lKgH120CAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAUPXLzC6jIMVNboJeMo5THOLKqAIihwyfyESQWduZeiuSbX7fFxGRBM6oLNOnAbd08yuQnBlyNcfvTTN5vTc9LADy0bn9ukS4ZPwTNOSXWa6FcopzoKALwlVAY3JzGxoFOHK9bImlUNAbpel1xkJt0z+/yGdUWvYsRYRhLuMidb34Spi8Kt5gbbY+EQcgkHK99GNamSOqrUR1pEKOWCpncFUqXhSSSOzZlv+pbXUwEbQwUYVhS1n8kRfzkXMjuN+Ke++YFOfB314MT0pSgCtSDEboOrXIAm4HmPMyek7YZdrQqGAIWEYUafx132D954JNytzQEElKeyvbmdQJWapoWA=="], "priority": ["100"], "algorithm": ["RSA-OAEP"]}}, {"id": "fdb97a8d-439a-4ead-adf9-a80696fbceab", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["15eca0bc-c3dd-48b9-a184-374691b3f1b2"], "secret": ["qQMFjvi_x6lbFNJBiyJV6Q"], "priority": ["100"]}}, {"id": "b6090ad3-b34f-4bc9-b331-ca0e734bfc09", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpQIBAAKCAQEApBKpDmj7d6UubF1b1NUnrRYKKDaJQDitv/7cGP2Erq7apdDdT2qdtRAhyAgBlNHT8mOL3fR0NM5/s+TNE7jtD07pSffcWpsyemILvoxfNxlqHVRjzbkW6qsrwjoVcAjVuKTdfZu3Egpcn53piiRbbOjcACuTQ4MN6B51s7indLWEjDMXMXcA4QsGoOr8Gcsj1azucz+JXrettgKlwmmjf4ciiSm+zvYtcXzj1TlM9tKji9G1vxRgc8vFVj7wgqtr0XU3sEQtHJsKuxQpmzOW7kfwD9+tOQdQs+zG3YAftsdM5xdCG2d98dxTMFekH+zyCTphjqw9nBJyZxgpNPQQNQIDAQABAoIBAALaE5x4y9GbJbMj7W2PDu7rjAI4pQYXUYuLXRiX0crfggu2FWdE1tzsQW6iqY/NgnS/1aNmTBSCU4HsH1WRSHRv8vxrlaXMYxSDyFbDRdxp49FwaBOyfMRYBwr5gleZCyTTVoCIJBR7VsTQoiUjIKTWjQxnIzYnM5SuJ24qShNsXk3FsqD3xGMAy5yEaezk/PwqnCI2x6n1rY+6nUGcjMgrBiAd1PloiNVUGPkfOS4A765/1SStivlD0sLZf4Hx385JUwNpXlo3B+NxYYig2nvjg99wCJL8rv/bzNMxtSPSakRRmchAjI5Ibl8Bu+Q7P6GSbaVIyQtd01skzlNW7sECgYEA1GzWQCCrUKb6cld3UrzMGafjfyZBgaavR+Mv26RW/5f/G9cCkpqreTUurS4XhPmmQ+VLge6eVqzRv4Ym0P+gOpmhtjyVRswckrHfXlyAsxDDEVWCqd+55TVaP58xXfwDmvM2cNX3VV8GBWz3CpxRjg39POqS9V5XYENckZcv+20CgYEAxbqu33St+fU5aAJNjPIj7DDU3FWl1boK8f71oERSDeFIVFMiPYTKnh1Gso7yAaEi4JR2W+Tq+e3Fr9CMtcHJPeUxR3+rKhV/FnXygPQQxSKblttjPRmU4x8r1VrCxV3j6G3niWP9v+tJmp5vCZDmGJvPtvkUElX/rQzGHQJa4ukCgYEAp6ZaFXRzII0YPeOwBbmBb4IkNvZvXq65yYeQ/s0d6E0ChutTWXb+fAOwGfe1kjohsZYm2J4FkyyDwZ9TxUDqKTWU/imkOqLerhv5yRRCcpB5E5YkMIOkyDQqTOoL4TwDcGghAD6uHE+JhgCwnEwwxWdfBJOxdjuFxDCFv1XOBaUCgYEAk9YIebpbedAHo+RU51UD1syL2lGPNEVteZIPREzQrOALnECsESL/+0gYW9jUyLMZyKA3/m0h4swMSm+7RQom1MhQGA00hLqd0sVNE21oPHyrqBdQE5bU4xujQloh1j9k75t8yhSg3KWm2pwqmRRldXDJ1RxWDX+PMmuilH32RkECgYEAuf1N0W7MKWNbnM/0elcdm+IIYI0kg4qRUKNyLg2vrmOTxwGTFzh1qeJK5x8vUMonnLW5vP7wIX7cTrgh79fz+W7gxXoNeuljnoZiFdk1Qu4qLFFk+c7MOGTDZZiKXTdYSgGutWptcGtgYli+x7TetFXVOxS3ppszgGdZ61onCSI="], "keyUse": ["SIG"], "certificate": ["MIICnTCCAYUCBgGCxUspezANBgkqhkiG9w0BAQsFADASMRAwDgYDVQQDDAdxdWFya3VzMB4XDTIyMDgyMjExMjE1M1oXDTMyMDgyMjExMjMzM1owEjEQMA4GA1UEAwwHcXVhcmt1czCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKQSqQ5o+3elLmxdW9TVJ60WCig2iUA4rb/+3Bj9hK6u2qXQ3U9qnbUQIcgIAZTR0/Jji930dDTOf7PkzRO47Q9O6Un33FqbMnpiC76MXzcZah1UY825FuqrK8I6FXAI1bik3X2btxIKXJ+d6YokW2zo3AArk0ODDegedbO4p3S1hIwzFzF3AOELBqDq/BnLI9Ws7nM/iV63rbYCpcJpo3+HIokpvs72LXF849U5TPbSo4vRtb8UYHPLxVY+8IKra9F1N7BELRybCrsUKZszlu5H8A/frTkHULPsxt2AH7bHTOcXQhtnffHcUzBXpB/s8gk6YY6sPZwScmcYKTT0EDUCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAiscT2FdZOg4rWVvL2IuDsDI6hzLJaoImDKfzSD00eY9SbqxjjUpVJlXkj+KUkJtgk+wh01gSY5s0ePW/S54T5kSAL8GNbfnRr/d6X7TmfQPgZ/+BvVwjdl5PKMtN4ISoLZqdMqjCXEXsYgrd3t+2TUfXD66DoUMRUqaQ8mqsfa1kpZOgXaJKZveiOPer4Y/SsJmp2p7CKoaSCRFMEjv2N98PiYx3TQxWtNx8BOs/3NKmIQiW5IM7PfSTV3Zxutd+LQkii0xLh5heOHHi7WP166n5Mq31OBRcm/u6k9GdPAMxPXh0JGqzKrRZPiXw3Mip9v4VXyQvo9xjrtIThQn5uQ=="], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "aa90914c-d3f2-41b7-a3db-31bf9d56f159", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "8f9aca4e-1af0-4d3a-acc0-3dea78658f3f", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "basic-auth-otp", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "2f6b23b9-2c59-4b65-ab00-2789b296f300", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "e53b15a7-5bf9-4064-8db6-8a82e8320f47", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "898b7008-e384-4df8-bca4-add09c5551d3", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "31fb288c-b29e-4370-93cf-ae6eca103063", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "0fc26a54-e662-4bfc-8bad-b16e4f9702a3", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "fed7521d-4732-4959-84f8-cff994343b90", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "e7013c43-177c-48ce-bcc8-b1d3e6f57280", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "497b288a-1cf1-4974-bbed-747b0ee5c1f8", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "27505ab1-6d4c-43a0-aede-d21ea45cb785", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "6bfb83ac-b5de-47f9-bc4f-9f27b44d05fb", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "228dbd4d-831b-486e-900b-294b9b82766c", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "f4eab5c7-a717-4646-9b3d-751e72ebbea6", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "e63a23e9-60f0-4d94-8481-3a3355d36ac3", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "0e1f6a40-583d-4db5-8516-bf1f7b2f1656", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Authentication Options", "userSetupAllowed": false}]}, {"id": "5380b8c5-e19f-4cca-9c66-383f05385136", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "1a7bcec3-694c-4934-b164-09754f9eb926", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-profile-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "57b0d393-33ff-46cc-bba0-ea476ce09b36", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "35a457ca-fb99-4d62-9020-0c3bda5452ab", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "19668cee-4ea3-4045-9b56-a8c1ff191de9", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "70a075e5-e708-4da9-8ae8-d6ea0c87b144", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "oauth2DevicePollingInterval": "5", "parRequestUriLifespan": "60", "cibaInterval": "5"}, "users": [{"id": "af134cab-f41c-4675-b141-205f975db679", "username": "admin", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "password", "hashedSaltedValue": "NICTtwsvSxJ5hL8hLAuleDUv9jwZcuXgxviMXvR++cciyPtiIEStEaJUyfA9DOir59awjPrHOumsclPVjNBplA==", "salt": "T/2P5o5oxFJUEk68BRURRg==", "hashIterations": 27500, "counter": 0, "algorithm": "pbkdf2-sha256", "digits": 0, "period": 0, "createdDate": 1554245879354, "config": {}}], "disableableCredentialTypes": ["password"], "requiredActions": [], "realmRoles": ["admin", "user"], "notBefore": 0, "groups": []}, {"id": "eb4123a3-b722-4798-9af5-8957f823657a", "username": "alice", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "password", "hashedSaltedValue": "A3okqV2T/ybXTVEgKfosoSjP8Yc9IZbFP/SY4cEd6hag7TABQrQ6nUSuwagGt96l8cw1DTijO75PqX6uiTXMzw==", "salt": "sl4mXx6T9FypPH/s9TngfQ==", "hashIterations": 27500, "counter": 0, "algorithm": "pbkdf2-sha256", "digits": 0, "period": 0, "createdDate": 1554245879116, "config": {}}], "disableableCredentialTypes": ["password"], "requiredActions": [], "realmRoles": ["user"], "notBefore": 0, "groups": []}, {"id": "1eed6a8e-a853-4597-b4c6-c4c2533546a0", "username": "jdoe", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "password", "hashedSaltedValue": "JV3DUNLjqOadjbBOtC4rvacQI553CGaDGAzBS8MR5ReCr7SwF3E6CsW3T7/XO8ITZAsch8+A/6loeuCoVLLJrg==", "salt": "uCbOH7HZtyDtMd0E9DG/nw==", "hashIterations": 27500, "counter": 0, "algorithm": "pbkdf2-sha256", "digits": 0, "period": 0, "createdDate": 1554245879227, "config": {}}], "disableableCredentialTypes": ["password"], "requiredActions": [], "realmRoles": ["confidential", "user"], "notBefore": 0, "groups": []}], "keycloakVersion": "19.0.1", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}