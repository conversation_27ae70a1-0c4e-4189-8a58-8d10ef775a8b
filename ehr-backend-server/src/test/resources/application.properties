# Test configuration
quarkus.datasource.db-kind=h2
quarkus.datasource.username=sa
quarkus.datasource.password=
quarkus.datasource.jdbc.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE

# Disable Flyway for tests
quarkus.flyway.migrate-at-start=false

# Hibernate configuration for tests
quarkus.hibernate-orm.database.generation=drop-and-create
quarkus.hibernate-orm.sql-load-script=no-file

# Logging
quarkus.log.level=INFO
quarkus.log.category."sirobilt.meghasanjivini".level=DEBUG

# Duplicate Detection Configuration for tests
duplicate.detection.enabled=true
duplicate.detection.threshold.high=85
duplicate.detection.threshold.medium=70
duplicate.detection.timeout.seconds=2
duplicate.detection.audit.enabled=false
duplicate.detection.batch.enabled=true

# Management endpoints configuration
quarkus.smallrye-health.root-path=/health
quarkus.smallrye-metrics.path=/metrics
quarkus.smallrye-openapi.path=/openapi
quarkus.swagger-ui.path=/swagger-ui

# Enable management endpoints
quarkus.smallrye-health.enabled=true
quarkus.smallrye-metrics.enabled=true
quarkus.smallrye-openapi.enabled=true
quarkus.swagger-ui.enabled=true

# Test HTTP port
quarkus.http.test-port=8081
