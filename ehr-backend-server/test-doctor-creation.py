#!/usr/bin/env python3
"""
Test script to verify that the Doctor creation API works correctly with the full_name field fix.
"""

import requests
import json
from datetime import datetime

# Test data for creating a doctor
test_doctor = {
    "firstName": "Aa",
    "lastName": "rma", 
    "middleName": "R",
    "specialization": "Cardiology",
    "roleType": "Consultant",
    "qualification": "MBBS, MD",
    "gender": "Female",
    "dateOfBirth": "1980-07-15",
    "mobileNumber": "9872344221",
    "email": "<EMAIL>",
    "registrationNumber": "**********",
    "registrationState": "Karnataka",
    "yearsOfExperience": 15,
    "telemedicineReady": True,
    "languagesSpoken": ["English", "Hindi", "Kannada"],
    "isActive": True,
    "address": {
        "street": "12 Residency Road",
        "city": "Bengaluru",
        "state": "Karnataka",
        "zipCode": "560025",
        "country": "India"
    }
}

def test_doctor_creation():
    """Test creating a doctor via the API"""
    
    # Assuming the API is running on localhost:8080
    url = "http://localhost:8080/doctors"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("Testing Doctor Creation API...")
        print(f"URL: {url}")
        print(f"Payload: {json.dumps(test_doctor, indent=2)}")
        
        response = requests.post(url, json=test_doctor, headers=headers, timeout=10)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 201:
            print("✅ SUCCESS: Doctor created successfully!")
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2)}")
            
            # Check if fullName is present in response
            if 'fullName' in response_data:
                print(f"✅ Full Name: {response_data['fullName']}")
            else:
                print("⚠️  Warning: fullName not present in response")
                
        elif response.status_code == 409:
            print("⚠️  CONFLICT: Registration number already exists")
            print(f"Response: {response.text}")
        else:
            print(f"❌ ERROR: Unexpected status code {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Could not connect to the API. Make sure the server is running on localhost:8080")
    except requests.exceptions.Timeout:
        print("❌ ERROR: Request timed out")
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")

if __name__ == "__main__":
    test_doctor_creation()
