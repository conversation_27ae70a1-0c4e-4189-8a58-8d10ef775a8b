# ABHA Authentication API

## Overview

The ABHA Authentication API provides a single orchestrated endpoint that handles the complete ABHA (Ayushman Bharat Health Account) search and authentication workflow following the ABDM (Ayushman Bharat Digital Mission) V3 specification.

This API consolidates the 3-step ABDM V3 process into a single request:
1. **Search ABHA** using mobile number (`/v3/profile/account/abha/search`)
2. **Generate OTP** for the selected ABHA (`/v3/profile/login/request/otp`)
3. **Verify OTP** and complete authentication (`/v3/profile/login/verify`)

## API Endpoint

### POST /abha/authenticate

Orchestrates the complete ABHA authentication workflow in a single API call.

#### Request

```json
{
  "mobile": "**********",
  "otp": "123456",
  "abhaIndex": 1
}
```

**Parameters:**
- `mobile` (string, required): 10-digit Indian mobile number
- `otp` (string, required): 6-digit OTP received on the mobile
- `abhaIndex` (integer, optional): Index of ABHA to authenticate (defaults to first ABHA found)

#### Response

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "ABHA authentication successful",
  "abhaProfile": {
    "abhaNumber": "91-**************",
    "abhaAddress": "john.doe@abdm",
    "name": "John Doe",
    "firstName": "John",
    "middleName": null,
    "lastName": "Doe",
    "gender": "M",
    "dateOfBirth": "1990-01-01",
    "mobile": "**********",
    "email": "<EMAIL>",
    "address": "123 Main St, City, State",
    "profilePhoto": "base64-encoded-image",
    "kycVerified": true,
    "authMethods": ["otp", "biometric"]
  },
  "authToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "txnId": "d0660ae0-1798-4e42-8e07-33eea4a3824d"
}
```

**Error Response (400/401/404/500):**
```json
{
  "success": false,
  "message": "No ABHA found for mobile number",
  "errorCode": "NO_ABHA_FOUND",
  "errorDetails": "No ABHA accounts are associated with this mobile number"
}
```

## Error Codes

| Error Code | HTTP Status | Description |
|------------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Invalid request parameters |
| `SESSION_ERROR` | 400 | Failed to get ABDM session token |
| `PUBLIC_KEY_ERROR` | 400 | Failed to retrieve encryption key |
| `SEARCH_ERROR` | 400 | ABHA search failed |
| `NO_ABHA_FOUND` | 404 | No ABHA found for mobile number |
| `INVALID_INDEX` | 401 | Invalid ABHA index specified |
| `OTP_GENERATION_ERROR` | 400 | Failed to generate OTP |
| `OTP_VERIFICATION_ERROR` | 401 | Invalid OTP or OTP expired |
| `INTERNAL_ERROR` | 500 | Internal server error |

## Health Check

### GET /abha/authenticate/health

Returns the health status of the ABHA authentication service.

**Response (200 OK):**
```json
{
  "service": "ABHA Authentication Service",
  "status": "UP",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0"
}
```

## Security Features

### RSA Encryption
- Mobile numbers are encrypted using ABDM public key before transmission
- ABHA indices are encrypted for OTP generation
- OTP values are encrypted before verification

### Request Headers
All ABDM API calls include required headers:
- `REQUEST-ID`: Unique UUID for request tracking
- `TIMESTAMP`: ISO 8601 timestamp
- `Authorization`: Bearer token from ABDM session

### Input Validation
- Mobile number format validation (10-digit Indian mobile)
- OTP format validation (6-digit numeric)
- ABHA index validation (positive integer)

## Configuration

### Environment Variables
```properties
# ABDM Configuration
abdm.client-id=your-client-id
abdm.client-secret=your-client-secret
abdm.environment=sandbox

# REST Client URLs
quarkus.rest-client.abdm-session.url=https://dev.abdm.gov.in/api/hiecm/gateway/v3
quarkus.rest-client.abdm-profile.url=https://abhasbx.abdm.gov.in/abha/api
quarkus.rest-client.abdm-login.url=https://abhasbx.abdm.gov.in/abha/api
quarkus.rest-client.abdm-publickey.url=https://abhasbx.abdm.gov.in/abha/api/v3/profile
```

## Usage Examples

### cURL Example
```bash
curl -X POST http://localhost:8080/abha/authenticate \
  -H "Content-Type: application/json" \
  -d '{
    "mobile": "**********",
    "otp": "123456"
  }'
```

### JavaScript Example
```javascript
const response = await fetch('/abha/authenticate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    mobile: '**********',
    otp: '123456',
    abhaIndex: 1
  })
});

const result = await response.json();
if (result.success) {
  console.log('ABHA Profile:', result.abhaProfile);
} else {
  console.error('Authentication failed:', result.message);
}
```

### Java Example
```java
AbhaAuthenticationRequest request = new AbhaAuthenticationRequest(
    "**********",
    "123456",
    1
);

AbhaAuthenticationResponse response = abhaAuthenticationService.authenticateAbha(request);

if (response.getSuccess()) {
    AbhaProfileDetails profile = response.getAbhaProfile();
    System.out.println("ABHA Number: " + profile.getAbhaNumber());
} else {
    System.err.println("Error: " + response.getMessage());
}
```

## Workflow Details

### Step 1: ABHA Search
- Encrypts mobile number using ABDM public key
- Calls `/v3/profile/account/abha/search` endpoint
- Returns list of ABHA accounts associated with mobile

### Step 2: OTP Generation
- Selects ABHA based on provided index or defaults to first
- Encrypts ABHA index for security
- Calls `/v3/profile/login/request/otp` endpoint
- Returns transaction ID for OTP verification

### Step 3: OTP Verification
- Encrypts provided OTP using ABDM public key
- Calls `/v3/profile/login/verify` endpoint
- Returns complete ABHA profile and authentication token

## Performance Considerations

### Caching
- Session tokens are cached for 15 minutes
- Public keys are cached for 1 hour
- Reduces API calls to ABDM gateway

### Monitoring
- Comprehensive logging for debugging
- Micrometer metrics for performance monitoring
- Health check endpoint for service monitoring

## Testing

### Unit Tests
Run unit tests for the service layer:
```bash
mvn test -Dtest=AbhaAuthenticationServiceTest
```

### Integration Tests
Run integration tests for the controller:
```bash
mvn test -Dtest=AbhaAuthenticationControllerTest
```

### Manual Testing
Use the provided test scripts in the `test-scripts` directory for manual API testing.

## Troubleshooting

### Common Issues

1. **Session Token Errors**
   - Verify ABDM client credentials
   - Check network connectivity to ABDM gateway

2. **Encryption Errors**
   - Ensure public key is retrieved successfully
   - Verify RSA encryption implementation

3. **OTP Verification Failures**
   - Check OTP format (6 digits)
   - Verify OTP is not expired
   - Ensure correct transaction ID is used

### Logging
Enable debug logging for detailed troubleshooting:
```properties
quarkus.log.category."sirobilt.meghasanjivini.abhaservice".level=DEBUG
```

## Support

For technical support or questions about the ABHA Authentication API, please contact the development team or refer to the ABDM documentation at https://abdm.gov.in/
