# Enable CORS globally
quarkus.http.cors=false

# Allow frontend origin
quarkus.http.cors.origins=*

# Allowed HTTP methods
quarkus.http.cors.methods=GET,POST,PUT,DELETE,OPTIONS

# Allowed headers
quarkus.http.cors.headers=Authorization,Content-Type,Accept,Origin,User-Agent,T-Token

# Allow credentials (cookies, Authorization headers, etc.)
quarkus.http.cors.access-control-allow-credentials=true

# Max age for preflight caching
quarkus.http.cors.access-control-max-age=86400



quarkus.smallrye-openapi.path=/abdm/openapi
quarkus.swagger-ui.always-include=true
quarkus.swagger-ui.path=/abdm/swagger-ui


quarkus.http.host=0.0.0.0

quarkus.http.port=8081

quarkus.oidc.enabled=false


# to prefix every HTTP endpoint (including health checks, swagger, static, etc.)
quarkus.http.root-path=/api

# OR, if you only want to move your JAX-RS endpoints (and leave Swagger UI, health, metrics, etc at /q/*):
#quarkus.resteasy-reactive.root-path=/api

# Tell SmallRye where to load the private key for signing
# Windows absolute path (note the triple slash)
smallrye.jwt.sign.key.location=file:///D:/EHR-Quarkus/EHR-Server/src/main/resources/privateKey.pem

# src/main/resources/application.properties
quarkus.hibernate-orm.physical-naming-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy


# Removed problematic property: quarkus.datasource.agroal.connection-acquisition-timeout=PT30S

#--------------------------------------------------------------------

# ? ABDM credentials & env
abdm.client-id=${ABDM_CLIENT_ID}
abdm.client-secret=${ABDM_CLIENT_SECRET}


abdm.environment=sandbox


# Removed problematic property: quarkus.jackson.default-property-inclusion=non_null


# configure MP-RestClient
quarkus.rest-client."com.example.abdm.AbdmGatewayClient".url=${abdm.${abdm.environment}.base-url}
# Base ABHA API host
abdm.api.base-url=https://abhasbx.abdm.gov.in/abha/api

quarkus.rest-client.enrolment-url.url=https://abhasbx.abdm.gov.in/abha/api/v3
quarkus.rest-client.login-url.url=https://abhasbx.abdm.gov.in/abha/api/v3
quarkus.rest-client.profile-url.url=https://abhasbx.abdm.gov.in/abha/api/v3
quarkus.rest-client.publicKey-url.url=https://abhasbx.abdm.gov.in/abha/api/v3
quarkus.rest-client.verification-url.url=https://abhasbx.abdm.gov.in/abha/api/v3

quarkus.rest-client.session-url.url=https://dev.abdm.gov.in/api/hiecm/gateway/v3


# Milestone2 rest client urls *******************************************************
quarkus.rest-client.create-session.url=https://dev.abdm.gov.in/api/hiecm/gateway/v3
quarkus.rest-client.bridge-service.url=https://dev.abdm.gov.in/api/hiecm/gateway/v3
quarkus.rest-client.certificate.url=https://dev.abdm.gov.in/api/hiecm/gateway/v3
quarkus.rest-client.openid-config.url=https://dev.abdm.gov.in/api/hiecm/gateway/v3
quarkus.rest-client.sms-notify.url=https://dev.abdm.gov.in/api/hiecm/hip/v3
quarkus.rest-client.user-initiated-linking.url=https://dev.abdm.gov.in/api/hiecm/user-initiated-linking/v3

# Allow Quarkus to return responses even if Accept header isn't perfect match
quarkus.resteasy-reactive.ignore-accept-header=true


quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG
quarkus.log.category."io.quarkus.rest.client".level=DEBUG
quarkus.log.category."io.vertx.core".level=DEBUG

abdm.benefit-name=dummy-benefit

# Cache Configuration for Performance Optimization
# ABDM Session Token Cache - expires after 15 minutes (tokens typically valid for 30 minutes)
quarkus.cache.caffeine."abdm-session-tokens".expire-after-write=15M
quarkus.cache.caffeine."abdm-session-tokens".maximum-size=100

# ABDM Public Key Cache - expires after 1 hour (keys change infrequently)
quarkus.cache.caffeine."abdm-public-keys".expire-after-write=1H
quarkus.cache.caffeine."abdm-public-keys".maximum-size=10

# Enable cache metrics for monitoring
quarkus.cache.caffeine."abdm-session-tokens".metrics-enabled=true
quarkus.cache.caffeine."abdm-public-keys".metrics-enabled=true

# Micrometer metrics configuration
quarkus.micrometer.enabled=true
quarkus.micrometer.export.prometheus.enabled=true
quarkus.micrometer.export.prometheus.path=/q/metrics

# Performance monitoring
quarkus.log.category."sirobilt.meghasanjivini.abhaservice.service".level=INFO

