# OIDC Core Configuration
quarkus.oidc.auth-server-url=https://megha-dev.sirobilt.com/auth/realms/hospital-app
quarkus.oidc.client-id=React
quarkus.oidc.credentials.secret=VMKXdoLVHbiSsEJsUfQDS1DL5OYrzCWt
quarkus.oidc.application-type=web-app
quarkus.oidc.authentication.redirect-uri=https://megha-dev.sirobilt.com/aut/callback
quarkus.oidc.logout.path=/auth/logout



# Secure specific paths. The @Authenticated annotation on JAX-RS resources
# is often sufficient, but if you need path-based rules, be specific.
quarkus.http.auth.permission.authenticated.paths=/me
quarkus.http.auth.permission.authenticated.policy=authenticated

# Explicitly permit the OIDC callback path.
# This ensures it's never caught by an authentication requirement.
quarkus.http.auth.permission.public.paths=/callback
quarkus.http.auth.permission.public.policy=permit




# ? Optional: encrypt the token-state cookie for added security (must be a 16-character string)
quarkus.oidc.token-state-manager.encryption-secret=MySuperSecretLongKeyForOIDC123456


# ? Debugging
quarkus.log.category."io.quarkus.oidc".level=DEBUG
quarkus.log.category."io.quarkus.oidc.runtime".level=DEBUG

# ? Disable unused datasource and Hibernate ORM
quarkus.datasource.db-kind=none
quarkus.datasource.jdbc=false
quarkus.hibernate-orm.enabled=false
quarkus.datasource.devservices.enabled=false


quarkus.http.port=8084
quarkus.http.host=0.0.0.0
quarkus.http.root-path=/aut
quarkus.http.proxy.proxy-address-forwarding=true
quarkus.oidc.authentication.force-redirect-path=true