quarkus.datasource.db-kind=postgresql
quarkus.datasource.username=${POSTGRES_DB_USERNAME}
quarkus.datasource.password=${POSTGRES_DB_PASSWORD}
quarkus.datasource.jdbc.url=jdbc:postgresql://${POSTGRES_DB_HOST}/${POSTGRES_DB_DATABASE}
quarkus.hibernate-orm.database.generation=update


quarkus.http.cors=true
quarkus.http.cors.origins=*
quarkus.http.cors.methods=GET,POST,PUT,DELETE,OPTIONS
quarkus.http.cors.headers=Content-Type,Authorization,T-token



quarkus.smallrye-openapi.path=/q/openapi
quarkus.swagger-ui.always-include=true
quarkus.swagger-ui.path=/q/swagger-ui


quarkus.http.host=0.0.0.0
quarkus.http.port=8084

quarkus.oidc.enabled=false

quarkus.log.category."org.hibernate.type.descriptor.sql.BasicBinder".level=TRACE
quarkus.log.category."org.hibernate.SQL".level=DEBUG

##flyway
#quarkus.flyway.migrate-at-start=true
#quarkus.flyway.baseline-on-migrate=true
#quarkus.flyway.locations=classpath:db/migration
#
#
#quarkus.flyway.repair-at-start=true



# to prefix every HTTP endpoint (including health checks, swagger, static, etc.)
quarkus.http.root-path=/api

# OR, if you only want to move your JAX-RS endpoints (and leave Swagger UI, health, metrics, etc at /q/*):
#quarkus.resteasy-reactive.root-path=/api

# Tell SmallRye where to load the private key for signing
# Windows absolute path (note the triple slash)
smallrye.jwt.sign.key.location=file:///D:/EHR-Quarkus/EHR-Server/src/main/resources/privateKey.pem

# src/main/resources/application.properties
quarkus.hibernate-orm.physical-naming-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy


# Removed problematic property: quarkus.datasource.agroal.connection-acquisition-timeout=PT30S

#--------------------------------------------------------------------




twillio.base-url=${TWILIO_BASE_URL}




# Removed problematic property: quarkus.jackson.default-property-inclusion=non_null





quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG
quarkus.log.category."io.quarkus.rest.client".level=DEBUG
quarkus.log.category."io.vertx.core".level=DEBUG



# Micrometer metrics configuration
quarkus.micrometer.enabled=true
quarkus.micrometer.export.prometheus.enabled=true
quarkus.micrometer.export.prometheus.path=/q/metrics

# Performance monitoring
quarkus.log.category."sirobilt.meghasanjivini.twillioservices.service".level=INFO

twilio.account.sid=**********************************
twilio.auth.token=da1548a44ff809447be18d0baf042f57
twilio.verify.service.sid=VAc70670a05b04e0eab849ac5c8cdfaa66
