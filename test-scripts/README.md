# EHR Backend API Test Scripts

This directory contains comprehensive test scripts to validate all APIs and services in the EHR backend system.

## 📋 Overview

The test suite includes:
- **Health Check Scripts** - Monitor service availability and health
- **API Test Scripts** - Validate all REST endpoints
- **Performance Tests** - Check response times and system performance
- **Error Handling Tests** - Validate error scenarios and edge cases
- **Integration Tests** - Test complete workflows

## 🚀 Quick Start

### Prerequisites

```bash
# Required
curl

# Recommended (for better output formatting)
jq
bc

# Optional (for advanced Python tests)
python3
requests  # pip install requests
```

### Run All Tests

```bash
# Make scripts executable
chmod +x test-scripts/*.sh

# Run all tests with default settings
./test-scripts/run-all-tests.sh

# Run tests against specific URL
./test-scripts/run-all-tests.sh http://localhost:8080

# Run tests with cleanup (removes test data)
./test-scripts/run-all-tests.sh http://localhost:8080 true
```

## 📁 Test Scripts

### 1. Master Test Runner
- **File**: `run-all-tests.sh`
- **Purpose**: Executes all test suites and provides comprehensive reporting
- **Usage**: `./run-all-tests.sh [BASE_URL] [CLEANUP]`

### 2. Health Check
- **File**: `health-check.sh`
- **Purpose**: Quick health monitoring for all services
- **Usage**: `./health-check.sh [BASE_URL]`
- **Features**:
  - Service availability check
  - Health endpoint validation
  - Database connectivity check
  - Performance monitoring

### 3. Comprehensive API Test
- **File**: `comprehensive-api-test.sh`
- **Purpose**: Tests all major API endpoints
- **Usage**: `./comprehensive-api-test.sh`
- **Coverage**:
  - Health checks
  - UPID configuration
  - Master data APIs
  - Patient registration
  - Duplicate detection
  - Error handling

### 4. Patient API Test
- **File**: `patient-api-test.sh`
- **Purpose**: Detailed testing of patient registration APIs
- **Usage**: `./patient-api-test.sh [BASE_URL] [--cleanup]`
- **Features**:
  - Patient registration
  - Patient search and retrieval
  - Patient updates
  - Validation testing
  - Performance testing

### 5. UPID Configuration Test
- **File**: `upid-config-test.sh`
- **Purpose**: Tests UPID configuration and generation
- **Usage**: `./upid-config-test.sh [BASE_URL]`
- **Features**:
  - Configuration retrieval
  - UPID generation
  - UPID validation
  - Admin configuration
  - Performance testing

### 6. Duplicate Detection Test
- **File**: `duplicate-detection-test.sh`
- **Purpose**: Tests duplicate detection and resolution
- **Usage**: `./duplicate-detection-test.sh [BASE_URL]`
- **Features**:
  - Manual duplicate checking
  - Batch processing
  - Statistics retrieval
  - Configuration management
  - Various matching scenarios

### 7. Advanced API Test (Python)
- **File**: `advanced-api-test.py`
- **Purpose**: Advanced testing with detailed validation
- **Usage**: `python3 advanced-api-test.py [BASE_URL]`
- **Features**:
  - Detailed response validation
  - Performance measurements
  - Comprehensive error handling
  - JSON response parsing

## 🔧 Configuration

### Environment Variables

```bash
# Set base URL (optional)
export EHR_BASE_URL="http://localhost:8080"

# Enable debug mode (optional)
export DEBUG=true
```

### Test Data

The scripts use the following test data patterns:
- **Facility IDs**: 1, 2, 999
- **Age Range**: 0-150 (as per system constraints)
- **Phone Numbers**: 10-digit Indian format
- **Email**: Valid email formats
- **ABHA Numbers**: Valid format patterns

## 📊 Test Reports

### Console Output
- Real-time test execution status
- Color-coded results (✓ Pass, ✗ Fail, ⚠ Warning)
- Performance metrics
- Error details

### Report Files
- Generated in `test-scripts/` directory
- Format: `test-report-YYYYMMDD-HHMMSS.txt`
- Contains summary and detailed results

## 🎯 Test Scenarios

### Health Checks
- Application health (`/q/health`)
- Readiness probe (`/q/health/ready`)
- Liveness probe (`/q/health/live`)
- Metrics endpoint (`/q/metrics`)

### Patient Registration
- Valid patient registration
- Invalid data validation
- Patient search and retrieval
- Patient updates
- Edge cases (special characters, minimal data)

### UPID Configuration
- Current configuration retrieval
- Available formats listing
- UPID generation for different facilities
- UPID validation (valid and invalid formats)
- Admin configuration management

### Duplicate Detection
- Manual duplicate checking
- Exact name matches
- Fuzzy name matching
- Phone number matching
- ABHA number matching
- Batch processing
- Statistics retrieval

### Master Data
- Lookup values retrieval
- Geography data (countries, states, districts)
- Facility suggestions

## 🚨 Troubleshooting

### Common Issues

1. **Service Not Running**
   ```bash
   # Check if service is running
   curl http://localhost:8080/q/health
   
   # Start the service
   cd ehr-backend-server
   ./mvnw quarkus:dev
   ```

2. **Permission Denied**
   ```bash
   # Make scripts executable
   chmod +x test-scripts/*.sh
   ```

3. **Missing Dependencies**
   ```bash
   # Install jq (Ubuntu/Debian)
   sudo apt-get install jq bc
   
   # Install jq (macOS)
   brew install jq bc
   
   # Install Python dependencies
   pip install requests
   ```

4. **Connection Refused**
   - Verify the base URL is correct
   - Check if the service is running on the expected port
   - Verify firewall settings

### Debug Mode

Enable verbose output:
```bash
# For bash scripts
bash -x ./test-scripts/health-check.sh

# For Python scripts
python3 -v ./test-scripts/advanced-api-test.py
```

## 📈 Performance Benchmarks

### Expected Response Times
- Health checks: < 100ms
- Patient list: < 2000ms
- UPID generation: < 100ms
- Duplicate detection: < 2000ms

### Performance Monitoring
The scripts automatically measure and report:
- Individual endpoint response times
- Overall test suite execution time
- Performance warnings for slow responses

## 🔄 Continuous Integration

### GitHub Actions Example
```yaml
name: API Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Start EHR Service
        run: |
          cd ehr-backend-server
          ./mvnw quarkus:dev &
          sleep 30
      - name: Run API Tests
        run: ./test-scripts/run-all-tests.sh
```

### Jenkins Pipeline
```groovy
pipeline {
    agent any
    stages {
        stage('Start Service') {
            steps {
                sh 'cd ehr-backend-server && ./mvnw quarkus:dev &'
                sleep 30
            }
        }
        stage('Run Tests') {
            steps {
                sh './test-scripts/run-all-tests.sh'
            }
        }
    }
}
```

## 📝 Contributing

To add new tests:
1. Create a new test script following the naming convention
2. Use the existing test helper functions
3. Add the script to `run-all-tests.sh`
4. Update this README

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review the test output for specific error messages
3. Verify service logs for backend issues
4. Ensure all prerequisites are installed
