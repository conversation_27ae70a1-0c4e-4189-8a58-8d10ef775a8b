#!/usr/bin/env python3
"""
EHR Backend Advanced API Test Suite
Comprehensive testing with detailed validation and reporting
"""

import requests
import json
import time
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import uuid

class EHRAPITester:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api"
        self.health_base = f"{base_url}/q"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        # Test tracking
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_results = []
        
    def log(self, message: str, level: str = "INFO"):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        colors = {
            "INFO": "\033[0;34m",
            "SUCCESS": "\033[0;32m", 
            "ERROR": "\033[0;31m",
            "WARNING": "\033[1;33m"
        }
        color = colors.get(level, "\033[0m")
        print(f"{color}[{timestamp}] {level}: {message}\033[0m")
    
    def run_test(self, test_name: str, test_func, *args, **kwargs) -> bool:
        """Run a test and track results"""
        self.total_tests += 1
        self.log(f"Running test: {test_name}")
        
        try:
            result = test_func(*args, **kwargs)
            if result:
                self.passed_tests += 1
                self.log(f"✓ {test_name}", "SUCCESS")
                self.test_results.append({"name": test_name, "status": "PASSED", "error": None})
                return True
            else:
                self.failed_tests += 1
                self.log(f"✗ {test_name}", "ERROR")
                self.test_results.append({"name": test_name, "status": "FAILED", "error": "Test returned False"})
                return False
        except Exception as e:
            self.failed_tests += 1
            self.log(f"✗ {test_name}: {str(e)}", "ERROR")
            self.test_results.append({"name": test_name, "status": "FAILED", "error": str(e)})
            return False
    
    def test_endpoint(self, method: str, endpoint: str, expected_status: int = 200, 
                     data: Optional[Dict] = None, params: Optional[Dict] = None) -> Tuple[bool, Optional[Dict]]:
        """Test an API endpoint"""
        try:
            url = endpoint if endpoint.startswith('http') else f"{self.api_base}{endpoint}"
            
            response = self.session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                timeout=30
            )
            
            success = response.status_code == expected_status
            
            if not success:
                self.log(f"Expected status {expected_status}, got {response.status_code}", "ERROR")
                self.log(f"Response: {response.text}", "ERROR")
            
            try:
                response_data = response.json() if response.content else None
            except:
                response_data = {"raw_response": response.text}
                
            return success, response_data
            
        except Exception as e:
            self.log(f"Request failed: {str(e)}", "ERROR")
            return False, None
    
    def test_health_endpoints(self):
        """Test all health check endpoints"""
        self.log("=== HEALTH CHECK TESTS ===")
        
        health_endpoints = [
            ("/health", "Application Health"),
            ("/health/ready", "Readiness Check"),
            ("/health/live", "Liveness Check"),
            ("/metrics", "Metrics Endpoint"),
            ("/openapi", "OpenAPI Specification")
        ]
        
        for endpoint, name in health_endpoints:
            self.run_test(
                name,
                lambda ep=endpoint: self.test_endpoint("GET", f"{self.health_base}{ep}")[0]
            )
    
    def test_upid_configuration(self):
        """Test UPID configuration endpoints"""
        self.log("=== UPID CONFIGURATION TESTS ===")
        
        # Test current configuration
        self.run_test(
            "Get Current UPID Configuration",
            lambda: self.test_endpoint("GET", "/upid-config/current")[0]
        )
        
        # Test available formats
        self.run_test(
            "Get Available UPID Formats", 
            lambda: self.test_endpoint("GET", "/upid-config/formats")[0]
        )
        
        # Test health check
        self.run_test(
            "UPID Health Check",
            lambda: self.test_endpoint("GET", "/upid-config/health")[0]
        )
        
        # Test sample generation
        self.run_test(
            "Generate Sample UPID",
            lambda: self.test_endpoint("POST", "/upid-config/generate-sample/1")[0]
        )
        
        # Test UPID validation
        validation_data = {"upid": "001-00-0000-0001"}
        self.run_test(
            "Validate UPID Format",
            lambda: self.test_endpoint("POST", "/upid-config/validate", data=validation_data)[0]
        )
        
        # Test admin endpoints
        self.run_test(
            "Get Effective Configuration (Admin)",
            lambda: self.test_endpoint("GET", "/upid-config/admin/effective")[0]
        )
    
    def test_master_data(self):
        """Test master data endpoints"""
        self.log("=== MASTER DATA TESTS ===")
        
        # Test lookup values
        self.run_test(
            "Get All Lookup Values",
            lambda: self.test_endpoint("GET", "/lookup-values")[0]
        )
        
        # Test geography data
        self.run_test(
            "Get Countries",
            lambda: self.test_endpoint("GET", "/geo/countries")[0]
        )
        
        # Test facility suggestions
        self.run_test(
            "Facility Suggestions",
            lambda: self.test_endpoint("GET", "/facilities/suggest", params={"name": "hospital"})[0]
        )
    
    def test_patient_registration(self):
        """Test patient registration endpoints"""
        self.log("=== PATIENT REGISTRATION TESTS ===")
        
        # Test patient listing
        self.run_test(
            "List All Patients",
            lambda: self.test_endpoint("GET", "/patients")[0]
        )
        
        # Test patient search
        self.run_test(
            "Patient Query Search",
            lambda: self.test_endpoint("GET", "/patients/query", params={"page": 0, "size": 10})[0]
        )
        
        # Test patient registration
        patient_data = {
            "facilityId": "2",
            "firstName": "Jane",
            "lastName": "Smith",
            "dateOfBirth": "1985-05-15",
            "gender": "FEMALE",
            "age": 38,
            "identifierType": "ABHA",
            "identifierNumber": f"98-7654-3210-{str(uuid.uuid4())[:4]}",
            "mobile": "**********",
            "email": "<EMAIL>",
            "address": {
                "addressLine1": "456 Oak Avenue",
                "city": "Delhi",
                "state": "Delhi",
                "pincode": "110001",
                "country": "India"
            }
        }
        
        self.run_test(
            "Register New Patient",
            lambda: self.test_endpoint("POST", "/patients", expected_status=201, data=patient_data)[0]
        )
        
        return patient_data
    
    def test_duplicate_detection(self, patient_data: Dict):
        """Test duplicate detection endpoints"""
        self.log("=== DUPLICATE DETECTION TESTS ===")
        
        # Test manual duplicate check
        self.run_test(
            "Manual Duplicate Check",
            lambda: self.test_endpoint("POST", "/patients/duplicates/check", data=patient_data)[0]
        )
        
        # Test statistics
        self.run_test(
            "Get Duplicate Detection Statistics",
            lambda: self.test_endpoint("GET", "/patients/duplicates/statistics", params={"days": 30})[0]
        )
        
        # Test pending reviews
        self.run_test(
            "Get Pending Duplicate Reviews",
            lambda: self.test_endpoint("GET", "/patients/duplicates/pending")[0]
        )
    
    def test_admin_configuration(self):
        """Test admin configuration endpoints"""
        self.log("=== ADMIN CONFIGURATION TESTS ===")
        
        # Test duplicate detection config
        self.run_test(
            "Get Duplicate Detection Configuration",
            lambda: self.test_endpoint("GET", "/admin/duplicate-detection/config")[0]
        )
    
    def test_error_handling(self):
        """Test error handling scenarios"""
        self.log("=== ERROR HANDLING TESTS ===")
        
        # Test invalid patient registration
        invalid_patient = {"firstName": "John"}
        self.run_test(
            "Invalid Patient Registration",
            lambda: self.test_endpoint("POST", "/patients", expected_status=400, data=invalid_patient)[0]
        )
        
        # Test invalid UPID validation
        invalid_upid = {"upid": "invalid-upid-format"}
        self.run_test(
            "Invalid UPID Validation",
            lambda: self.test_endpoint("POST", "/upid-config/validate", data=invalid_upid)[0]
        )
        
        # Test non-existent endpoint
        self.run_test(
            "Non-existent Endpoint",
            lambda: self.test_endpoint("GET", "/non-existent", expected_status=404)[0]
        )
    
    def test_performance(self):
        """Test API performance"""
        self.log("=== PERFORMANCE TESTS ===")
        
        def measure_response_time(endpoint: str, method: str = "GET", max_time: float = 2.0) -> bool:
            start_time = time.time()
            success, _ = self.test_endpoint(method, endpoint)
            response_time = time.time() - start_time
            
            if response_time > max_time:
                self.log(f"Response time {response_time:.2f}s exceeds {max_time}s", "WARNING")
                return False
            else:
                self.log(f"Response time: {response_time:.2f}s", "INFO")
                return success
        
        self.run_test(
            "Health Check Performance (<2s)",
            lambda: measure_response_time("/health", max_time=2.0)
        )
        
        self.run_test(
            "Patient List Performance (<2s)",
            lambda: measure_response_time("/patients", max_time=2.0)
        )
    
    def run_all_tests(self):
        """Run all test suites"""
        self.log("Starting EHR Backend API Test Suite")
        self.log(f"Base URL: {self.base_url}")
        
        start_time = time.time()
        
        # Run test suites
        self.test_health_endpoints()
        self.test_upid_configuration()
        self.test_master_data()
        patient_data = self.test_patient_registration()
        self.test_duplicate_detection(patient_data)
        self.test_admin_configuration()
        self.test_error_handling()
        self.test_performance()
        
        # Print summary
        end_time = time.time()
        duration = end_time - start_time
        
        self.print_summary(duration)
        
        return self.failed_tests == 0
    
    def print_summary(self, duration: float):
        """Print test summary"""
        print("\n" + "="*50)
        print("TEST SUMMARY")
        print("="*50)
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: \033[0;32m{self.passed_tests}\033[0m")
        print(f"Failed: \033[0;31m{self.failed_tests}\033[0m")
        print(f"Duration: {duration:.2f} seconds")
        
        if self.failed_tests > 0:
            print("\nFailed Tests:")
            for result in self.test_results:
                if result["status"] == "FAILED":
                    print(f"  - {result['name']}: {result['error']}")
        
        if self.failed_tests == 0:
            print("\n\033[0;32m🎉 All tests passed!\033[0m")
        else:
            print(f"\n\033[0;31m❌ {self.failed_tests} test(s) failed\033[0m")

def main():
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8080"
    
    tester = EHRAPITester(base_url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
