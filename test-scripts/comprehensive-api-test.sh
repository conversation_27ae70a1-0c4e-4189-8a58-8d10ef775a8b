#!/bin/bash

# EHR Backend API Comprehensive Test Script
# Tests all APIs and services for functionality and health

set -e

# Configuration
BASE_URL="http://localhost:8080"
API_BASE="${BASE_URL}/api"
HEALTH_BASE="${BASE_URL}/q"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✓${NC} $1"
    ((PASSED_TESTS++))
}

error() {
    echo -e "${RED}✗${NC} $1"
    ((FAILED_TESTS++))
}

warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Test function wrapper
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TOTAL_TESTS++))
    log "Running test: $test_name"
    
    if eval "$test_command"; then
        success "$test_name"
    else
        error "$test_name"
    fi
    echo
}

# HTTP test helper
test_endpoint() {
    local method="$1"
    local endpoint="$2"
    local expected_status="$3"
    local data="$4"
    local content_type="${5:-application/json}"
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: $content_type' -d '$data'"
    fi
    
    curl_cmd="$curl_cmd '$endpoint'"
    
    local response=$(eval "$curl_cmd")
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        return 0
    else
        echo "Expected status $expected_status, got $status_code"
        echo "Response: $body"
        return 1
    fi
}

# Start testing
echo "=========================================="
echo "EHR Backend API Comprehensive Test Suite"
echo "=========================================="
echo "Base URL: $BASE_URL"
echo "Testing started at: $(date)"
echo

# 1. Health Check Tests
log "=== HEALTH CHECK TESTS ==="

run_test "Application Health Check" \
    "test_endpoint GET '$HEALTH_BASE/health' 200"

run_test "Application Readiness Check" \
    "test_endpoint GET '$HEALTH_BASE/health/ready' 200"

run_test "Application Liveness Check" \
    "test_endpoint GET '$HEALTH_BASE/health/live' 200"

run_test "Metrics Endpoint" \
    "test_endpoint GET '$HEALTH_BASE/metrics' 200"

run_test "OpenAPI Specification" \
    "test_endpoint GET '$HEALTH_BASE/openapi' 200"

# 2. UPID Configuration Tests
log "=== UPID CONFIGURATION TESTS ==="

run_test "Get Current UPID Configuration" \
    "test_endpoint GET '$API_BASE/upid-config/current' 200"

run_test "Get Available UPID Formats" \
    "test_endpoint GET '$API_BASE/upid-config/formats' 200"

run_test "UPID Health Check" \
    "test_endpoint GET '$API_BASE/upid-config/health' 200"

run_test "Generate Sample UPID" \
    "test_endpoint POST '$API_BASE/upid-config/generate-sample/1' 200"

run_test "Validate UPID Format" \
    "test_endpoint POST '$API_BASE/upid-config/validate' 200 '{\"upid\":\"001-00-0000-0001\"}'"

# Admin UPID endpoints
run_test "Get Effective UPID Configuration (Admin)" \
    "test_endpoint GET '$API_BASE/upid-config/admin/effective' 200"

# 3. Master Data Tests
log "=== MASTER DATA TESTS ==="

run_test "Get All Lookup Values" \
    "test_endpoint GET '$API_BASE/lookup-values' 200"

run_test "Get Countries" \
    "test_endpoint GET '$API_BASE/geo/countries' 200"

run_test "Facility Suggestions" \
    "test_endpoint GET '$API_BASE/facilities/suggest?name=hospital' 200"

# 4. Patient Registration Tests
log "=== PATIENT REGISTRATION TESTS ==="

run_test "List All Patients" \
    "test_endpoint GET '$API_BASE/patients' 200"

run_test "Patient Query Search" \
    "test_endpoint GET '$API_BASE/patients/query?page=0&size=10' 200"

# Test patient registration with valid data
PATIENT_DATA='{
    "facilityId": "2",
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "gender": "MALE",
    "age": 30,
    "identifierType": "ABHA",
    "identifierNumber": "12-**************",
    "mobile": "**********",
    "email": "<EMAIL>",
    "address": {
        "addressLine1": "123 Main St",
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400001",
        "country": "India"
    }
}'

run_test "Register New Patient" \
    "test_endpoint POST '$API_BASE/patients' 201 '$PATIENT_DATA'"

# 5. Duplicate Detection Tests
log "=== DUPLICATE DETECTION TESTS ==="

run_test "Manual Duplicate Check" \
    "test_endpoint POST '$API_BASE/patients/duplicates/check' 200 '$PATIENT_DATA'"

run_test "Get Duplicate Detection Statistics" \
    "test_endpoint GET '$API_BASE/patients/duplicates/statistics?days=30' 200"

run_test "Get Pending Duplicate Reviews" \
    "test_endpoint GET '$API_BASE/patients/duplicates/pending' 200"

# 6. Admin Configuration Tests
log "=== ADMIN CONFIGURATION TESTS ==="

run_test "Get Duplicate Detection Configuration" \
    "test_endpoint GET '$API_BASE/admin/duplicate-detection/config' 200"

# 7. Error Handling Tests
log "=== ERROR HANDLING TESTS ==="

run_test "Invalid Patient Registration (Missing Required Fields)" \
    "test_endpoint POST '$API_BASE/patients' 400 '{\"firstName\":\"John\"}'"

run_test "Invalid UPID Validation" \
    "test_endpoint POST '$API_BASE/upid-config/validate' 200 '{\"upid\":\"invalid-upid\"}'"

run_test "Non-existent Endpoint" \
    "test_endpoint GET '$API_BASE/non-existent' 404"

# Test Summary
echo "=========================================="
echo "TEST SUMMARY"
echo "=========================================="
echo "Total Tests: $TOTAL_TESTS"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}All tests passed! 🎉${NC}"
    exit 0
else
    echo -e "${RED}Some tests failed. Please check the output above.${NC}"
    exit 1
fi
