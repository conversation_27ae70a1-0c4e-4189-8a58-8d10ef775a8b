#!/bin/bash

# Duplicate Detection API Test Script
# Comprehensive testing of duplicate detection and resolution APIs

set -e

BASE_URL="${1:-http://localhost:8080}"
API_BASE="${BASE_URL}/api"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✓${NC} $1"
}

error() {
    echo -e "${RED}✗${NC} $1"
}

warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Test helper function
test_api() {
    local method="$1"
    local endpoint="$2"
    local expected_status="$3"
    local data="$4"
    local description="$5"
    
    log "Testing: $description"
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    curl_cmd="$curl_cmd '$API_BASE$endpoint'"
    
    local response=$(eval "$curl_cmd")
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        success "$description (HTTP $status_code)"
        if [ -n "$body" ] && [ "$body" != "null" ]; then
            echo "$body" | jq . 2>/dev/null || echo "Response: $body"
        fi
        echo
        return 0
    else
        error "$description (Expected $expected_status, got $status_code)"
        echo "Response: $body"
        echo
        return 1
    fi
}

echo "=========================================="
echo "Duplicate Detection API Test Suite"
echo "=========================================="
echo "Base URL: $BASE_URL"
echo "Testing started at: $(date)"
echo

# Generate unique identifier for testing
UNIQUE_ID=$(date +%s)

# 1. Configuration Tests
log "=== CONFIGURATION TESTS ==="

test_api "GET" "/admin/duplicate-detection/config" "200" "" "Get duplicate detection configuration"

# 2. Basic Duplicate Detection Tests
log "=== BASIC DUPLICATE DETECTION TESTS ==="

# Test patient data for duplicate detection
TEST_PATIENT='{
    "facilityId": "2",
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "gender": "MALE",
    "age": 30,
    "identifierType": "ABHA",
    "identifierNumber": "12-**************",
    "mobile": "**********",
    "email": "<EMAIL>",
    "address": {
        "addressLine1": "123 Main Street",
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400001",
        "country": "India"
    }
}'

test_api "POST" "/patients/duplicates/check" "200" "$TEST_PATIENT" "Manual duplicate check"

# 3. Statistics and Monitoring Tests
log "=== STATISTICS AND MONITORING TESTS ==="

test_api "GET" "/patients/duplicates/statistics?days=30" "200" "" "Get duplicate detection statistics (30 days)"

test_api "GET" "/patients/duplicates/statistics?days=7" "200" "" "Get duplicate detection statistics (7 days)"

test_api "GET" "/patients/duplicates/pending" "200" "" "Get pending duplicate reviews"

# 4. Different Duplicate Scenarios
log "=== DUPLICATE SCENARIO TESTS ==="

# Exact name match scenario
EXACT_MATCH_PATIENT='{
    "facilityId": "2",
    "firstName": "Jane",
    "lastName": "Smith",
    "dateOfBirth": "1985-05-15",
    "gender": "FEMALE",
    "age": 38,
    "identifierType": "ABHA",
    "identifierNumber": "98-**************",
    "mobile": "**********",
    "email": "<EMAIL>"
}'

test_api "POST" "/patients/duplicates/check" "200" "$EXACT_MATCH_PATIENT" "Exact name match duplicate check"

# Similar name scenario (fuzzy matching)
FUZZY_MATCH_PATIENT='{
    "facilityId": "2",
    "firstName": "Jon",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "gender": "MALE",
    "age": 30,
    "identifierType": "ABHA",
    "identifierNumber": "12-3456-7890-9999",
    "mobile": "**********"
}'

test_api "POST" "/patients/duplicates/check" "200" "$FUZZY_MATCH_PATIENT" "Fuzzy name match duplicate check"

# Same phone number scenario
PHONE_MATCH_PATIENT='{
    "facilityId": "2",
    "firstName": "Different",
    "lastName": "Person",
    "dateOfBirth": "1995-12-25",
    "gender": "FEMALE",
    "age": 28,
    "mobile": "**********"
}'

test_api "POST" "/patients/duplicates/check" "200" "$PHONE_MATCH_PATIENT" "Phone number match duplicate check"

# Same ABHA number scenario (should be high confidence)
ABHA_MATCH_PATIENT='{
    "facilityId": "2",
    "firstName": "Another",
    "lastName": "Person",
    "dateOfBirth": "1988-03-10",
    "gender": "MALE",
    "age": 35,
    "identifierType": "ABHA",
    "identifierNumber": "12-**************"
}'

test_api "POST" "/patients/duplicates/check" "200" "$ABHA_MATCH_PATIENT" "ABHA number match duplicate check"

# 5. Batch Processing Tests
log "=== BATCH PROCESSING TESTS ==="

BATCH_REQUEST='{
    "facilityIds": ["1", "2"],
    "maxRecords": 100,
    "onlyHighConfidence": true
}'

test_api "POST" "/patients/duplicates/batch" "200" "$BATCH_REQUEST" "Batch duplicate detection"

# 6. Edge Cases and Validation Tests
log "=== EDGE CASES AND VALIDATION TESTS ==="

# Empty patient data
EMPTY_PATIENT='{}'
test_api "POST" "/patients/duplicates/check" "400" "$EMPTY_PATIENT" "Empty patient data duplicate check"

# Minimal patient data
MINIMAL_PATIENT='{
    "facilityId": "2",
    "firstName": "Min",
    "lastName": "Patient"
}'
test_api "POST" "/patients/duplicates/check" "200" "$MINIMAL_PATIENT" "Minimal patient data duplicate check"

# Patient with special characters
SPECIAL_CHARS_PATIENT='{
    "facilityId": "2",
    "firstName": "José",
    "lastName": "García-López",
    "dateOfBirth": "1985-12-25",
    "gender": "MALE",
    "age": 38,
    "identifierType": "ABHA",
    "identifierNumber": "98-7654-3210-'$UNIQUE_ID'"
}'

test_api "POST" "/patients/duplicates/check" "200" "$SPECIAL_CHARS_PATIENT" "Special characters duplicate check"

# 7. Performance Tests
log "=== PERFORMANCE TESTS ==="

log "Testing duplicate detection performance..."
start_time=$(date +%s.%N)
curl -s -X POST -H 'Content-Type: application/json' -d "$TEST_PATIENT" "$API_BASE/patients/duplicates/check" > /dev/null
end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc -l)
duration_ms=$(echo "$duration * 1000" | bc -l | cut -d. -f1)

if (( duration_ms < 2000 )); then
    success "Duplicate detection performance: ${duration_ms}ms (< 2000ms)"
else
    warning "Duplicate detection performance: ${duration_ms}ms (>= 2000ms)"
fi

# 8. Configuration Update Tests (Admin)
log "=== CONFIGURATION UPDATE TESTS ==="

# Get current configuration first
CURRENT_CONFIG=$(curl -s "$API_BASE/admin/duplicate-detection/config")
log "Current configuration retrieved"

# Test configuration validation (without applying changes)
TEST_CONFIG='{
    "enabled": true,
    "highThreshold": 90,
    "mediumThreshold": 75,
    "weights": {
        "nameExact": 45,
        "nameFuzzy": 30,
        "dateOfBirth": 35,
        "phone": 25,
        "email": 20,
        "address": 15,
        "identifier": 40
    },
    "fuzzyThreshold": 85,
    "timeoutSeconds": 3,
    "auditEnabled": true
}'

# Note: We're not actually updating the config to avoid affecting the system
log "Configuration update test skipped to avoid system changes"

# 9. Relationship Tests
log "=== RELATIONSHIP TESTS ==="

# Try to get relationships for a test patient ID
# This might return empty results if no relationships exist
test_api "GET" "/patients/duplicates/relationships/001-00-0000-0001" "200" "" "Get patient relationships"

# 10. Error Handling Tests
log "=== ERROR HANDLING TESTS ==="

# Invalid facility ID in batch request
INVALID_BATCH='{
    "facilityIds": ["invalid"],
    "maxRecords": 10
}'

test_api "POST" "/patients/duplicates/batch" "400" "$INVALID_BATCH" "Invalid facility ID in batch request"

# Invalid patient ID for relationships
test_api "GET" "/patients/duplicates/relationships/invalid-id" "404" "" "Invalid patient ID for relationships"

echo
echo "=========================================="
echo "Duplicate Detection Test Summary"
echo "=========================================="
echo "All duplicate detection API tests completed."
echo "Check the output above for any failures."
echo
echo "Key test scenarios covered:"
echo "- Manual duplicate checking"
echo "- Exact name matches"
echo "- Fuzzy name matching"
echo "- Phone number matching"
echo "- ABHA number matching"
echo "- Batch processing"
echo "- Performance validation"
echo "- Error handling"
