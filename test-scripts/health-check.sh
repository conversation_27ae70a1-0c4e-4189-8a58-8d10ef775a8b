#!/bin/bash

# EHR Backend Health Check Script
# Quick health monitoring for all services

set -e

# Configuration
BASE_URL="${1:-http://localhost:8080}"
API_BASE="${BASE_URL}/api"
HEALTH_BASE="${BASE_URL}/q"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Health check function
check_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    printf "%-40s" "$name:"
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null "$url" 2>/dev/null || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✓ OK${NC} (HTTP $response)"
        return 0
    else
        echo -e "${RED}✗ FAIL${NC} (HTTP $response)"
        return 1
    fi
}

# Check if service is running
check_service_running() {
    printf "%-40s" "Service Availability:"
    
    if curl -s --connect-timeout 5 "$BASE_URL" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ RUNNING${NC}"
        return 0
    else
        echo -e "${RED}✗ NOT RUNNING${NC}"
        return 1
    fi
}

# Main health check
echo "=========================================="
echo "EHR Backend Health Check"
echo "=========================================="
echo "Target: $BASE_URL"
echo "Time: $(date)"
echo

# Check if service is running first
if ! check_service_running; then
    echo -e "\n${RED}❌ Service is not running. Please start the EHR backend service.${NC}"
    exit 1
fi

echo

# Core health endpoints
echo "Core Health Checks:"
echo "----------------------------------------"
check_endpoint "Application Health" "$HEALTH_BASE/health"
check_endpoint "Readiness Probe" "$HEALTH_BASE/health/ready"
check_endpoint "Liveness Probe" "$HEALTH_BASE/health/live"
check_endpoint "Metrics" "$HEALTH_BASE/metrics"

echo

# API health checks
echo "API Health Checks:"
echo "----------------------------------------"
check_endpoint "UPID Configuration Health" "$API_BASE/upid-config/health"
check_endpoint "Patient API" "$API_BASE/patients"
check_endpoint "Lookup Values API" "$API_BASE/lookup-values"
check_endpoint "Geography API" "$API_BASE/geo/countries"
check_endpoint "Facilities API" "$API_BASE/facilities/suggest?name=test"

echo

# Database connectivity (indirect check)
echo "Database Connectivity:"
echo "----------------------------------------"
check_endpoint "Patient Data Access" "$API_BASE/patients?page=0&size=1"
check_endpoint "Lookup Data Access" "$API_BASE/lookup-values"

echo

# Configuration checks
echo "Configuration Health:"
echo "----------------------------------------"
check_endpoint "UPID Config" "$API_BASE/upid-config/current"
check_endpoint "Duplicate Detection Config" "$API_BASE/admin/duplicate-detection/config"

echo

# Performance check
echo "Performance Check:"
echo "----------------------------------------"
printf "%-40s" "Response Time Test:"

start_time=$(date +%s.%N)
curl -s "$API_BASE/patients?page=0&size=1" > /dev/null
end_time=$(date +%s.%N)

response_time=$(echo "$end_time - $start_time" | bc -l)
response_time_ms=$(echo "$response_time * 1000" | bc -l | cut -d. -f1)

if (( response_time_ms < 2000 )); then
    echo -e "${GREEN}✓ GOOD${NC} (${response_time_ms}ms)"
else
    echo -e "${YELLOW}⚠ SLOW${NC} (${response_time_ms}ms)"
fi

echo

# Summary
echo "=========================================="
echo "Health Check Summary"
echo "=========================================="

# Count successful checks by re-running them silently
total_checks=0
passed_checks=0

endpoints=(
    "$HEALTH_BASE/health"
    "$HEALTH_BASE/health/ready" 
    "$HEALTH_BASE/health/live"
    "$HEALTH_BASE/metrics"
    "$API_BASE/upid-config/health"
    "$API_BASE/patients"
    "$API_BASE/lookup-values"
    "$API_BASE/geo/countries"
    "$API_BASE/upid-config/current"
    "$API_BASE/admin/duplicate-detection/config"
)

for endpoint in "${endpoints[@]}"; do
    total_checks=$((total_checks + 1))
    if curl -s -f "$endpoint" > /dev/null 2>&1; then
        passed_checks=$((passed_checks + 1))
    fi
done

echo "Total Checks: $total_checks"
echo -e "Passed: ${GREEN}$passed_checks${NC}"
echo -e "Failed: ${RED}$((total_checks - passed_checks))${NC}"

if [ $passed_checks -eq $total_checks ]; then
    echo -e "\n${GREEN}🎉 All health checks passed! System is healthy.${NC}"
    exit 0
else
    echo -e "\n${YELLOW}⚠ Some health checks failed. Please investigate.${NC}"
    exit 1
fi
