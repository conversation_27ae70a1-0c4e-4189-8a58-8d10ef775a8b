#!/bin/bash

# Patient Registration API Test Script
# Comprehensive testing of patient registration and management APIs

set -e

BASE_URL="${1:-http://localhost:8080}"
API_BASE="${BASE_URL}/api"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✓${NC} $1"
}

error() {
    echo -e "${RED}✗${NC} $1"
}

# Test helper function
test_api() {
    local method="$1"
    local endpoint="$2"
    local expected_status="$3"
    local data="$4"
    local description="$5"
    
    log "Testing: $description"
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    curl_cmd="$curl_cmd '$API_BASE$endpoint'"
    
    local response=$(eval "$curl_cmd")
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        success "$description (HTTP $status_code)"
        if [ -n "$body" ] && [ "$body" != "null" ]; then
            echo "Response: $body" | jq . 2>/dev/null || echo "Response: $body"
        fi
        return 0
    else
        error "$description (Expected $expected_status, got $status_code)"
        echo "Response: $body"
        return 1
    fi
}

echo "=========================================="
echo "Patient Registration API Test Suite"
echo "=========================================="
echo "Base URL: $BASE_URL"
echo "Testing started at: $(date)"
echo

# 1. Basic Patient API Tests
log "=== BASIC PATIENT API TESTS ==="

test_api "GET" "/patients" "200" "" "List all patients"

test_api "GET" "/patients?page=0&size=5" "200" "" "List patients with pagination"

test_api "GET" "/patients/query?page=0&size=10" "200" "" "Patient query endpoint"

# 2. Patient Registration Tests
log "=== PATIENT REGISTRATION TESTS ==="

# Generate unique identifier for testing
UNIQUE_ID=$(date +%s)

# Valid patient registration
VALID_PATIENT='{
    "facilityId": "2",
    "firstName": "TestPatient",
    "lastName": "User'$UNIQUE_ID'",
    "dateOfBirth": "1990-01-01",
    "gender": "MALE",
    "age": 30,
    "identifierType": "ABHA",
    "identifierNumber": "12-3456-7890-'$UNIQUE_ID'",
    "mobile": "98765432'$(echo $UNIQUE_ID | tail -c 3)'",
    "email": "test'$UNIQUE_ID'@example.com",
    "address": {
        "addressLine1": "123 Test Street",
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400001",
        "country": "India"
    },
    "emergencyContact": {
        "name": "Emergency Contact",
        "relationship": "SPOUSE",
        "mobile": "**********"
    }
}'

test_api "POST" "/patients" "201" "$VALID_PATIENT" "Register valid patient"

# Store the patient ID for further tests
PATIENT_RESPONSE=$(curl -s -X POST -H 'Content-Type: application/json' -d "$VALID_PATIENT" "$API_BASE/patients")
PATIENT_ID=$(echo "$PATIENT_RESPONSE" | jq -r '.patientId' 2>/dev/null || echo "")

if [ -n "$PATIENT_ID" ] && [ "$PATIENT_ID" != "null" ]; then
    log "Created patient with ID: $PATIENT_ID"
    
    # Test patient retrieval by ID
    test_api "GET" "/patients?upId=$PATIENT_ID" "200" "" "Retrieve patient by ID"
    
    # Test patient update
    UPDATE_DATA='{
        "firstName": "UpdatedTestPatient",
        "lastName": "UpdatedUser'$UNIQUE_ID'",
        "mobile": "98765432'$(echo $UNIQUE_ID | tail -c 3)'",
        "email": "updated'$UNIQUE_ID'@example.com"
    }'
    
    test_api "PUT" "/patients/$PATIENT_ID" "200" "$UPDATE_DATA" "Update patient details"
fi

# 3. Patient Search Tests
log "=== PATIENT SEARCH TESTS ==="

test_api "GET" "/patients?firstName=TestPatient" "200" "" "Search by first name"

test_api "GET" "/patients?mobile=**********" "200" "" "Search by mobile number"

test_api "GET" "/patients/query?query=TestPatient&page=0&size=10" "200" "" "Unified search query"

# 4. Validation Tests
log "=== VALIDATION TESTS ==="

# Missing required fields
INVALID_PATIENT_1='{
    "firstName": "John"
}'

test_api "POST" "/patients" "400" "$INVALID_PATIENT_1" "Invalid patient - missing required fields"

# Invalid email format
INVALID_PATIENT_2='{
    "facilityId": "2",
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "gender": "MALE",
    "age": 30,
    "email": "invalid-email"
}'

test_api "POST" "/patients" "400" "$INVALID_PATIENT_2" "Invalid patient - invalid email format"

# Invalid age
INVALID_PATIENT_3='{
    "facilityId": "2",
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "gender": "MALE",
    "age": 200
}'

test_api "POST" "/patients" "400" "$INVALID_PATIENT_3" "Invalid patient - invalid age"

# 5. Edge Cases
log "=== EDGE CASE TESTS ==="

# Minimum valid patient
MINIMAL_PATIENT='{
    "facilityId": "2",
    "firstName": "Minimal",
    "lastName": "Patient'$UNIQUE_ID'",
    "dateOfBirth": "1990-01-01",
    "gender": "FEMALE",
    "age": 30
}'

test_api "POST" "/patients" "201" "$MINIMAL_PATIENT" "Register minimal valid patient"

# Patient with special characters
SPECIAL_PATIENT='{
    "facilityId": "2",
    "firstName": "José",
    "lastName": "García-López",
    "dateOfBirth": "1985-12-25",
    "gender": "MALE",
    "age": 35,
    "identifierType": "ABHA",
    "identifierNumber": "98-7654-3210-'$UNIQUE_ID'"
}'

test_api "POST" "/patients" "201" "$SPECIAL_PATIENT" "Register patient with special characters"

# 6. Performance Tests
log "=== PERFORMANCE TESTS ==="

log "Testing patient list performance..."
start_time=$(date +%s.%N)
curl -s "$API_BASE/patients?page=0&size=100" > /dev/null
end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc -l)
duration_ms=$(echo "$duration * 1000" | bc -l | cut -d. -f1)

if (( duration_ms < 2000 )); then
    success "Patient list performance: ${duration_ms}ms (< 2000ms)"
else
    error "Patient list performance: ${duration_ms}ms (>= 2000ms)"
fi

# 7. Cleanup (optional)
if [ "$2" = "--cleanup" ] && [ -n "$PATIENT_ID" ] && [ "$PATIENT_ID" != "null" ]; then
    log "=== CLEANUP ==="
    test_api "DELETE" "/patients/$PATIENT_ID" "200" "" "Delete test patient"
fi

echo
echo "=========================================="
echo "Patient API Test Summary"
echo "=========================================="
echo "All patient registration API tests completed."
echo "Check the output above for any failures."
echo
echo "To run with cleanup: $0 [BASE_URL] --cleanup"
