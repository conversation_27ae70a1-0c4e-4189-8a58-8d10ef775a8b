#!/usr/bin/env python3
"""
Quick Fix Validation Script
Tests the specific fixes for gender validation and other issues
"""

import requests
import json
import uuid
from datetime import datetime

def test_patient_registration_with_correct_gender():
    """Test patient registration with correct gender format"""
    url = "http://localhost:8080/api/patients"
    
    # Test data with correct gender format (Male/Female/Other)
    patient_data = {
        "facilityId": "2",
        "firstName": "TestFix",
        "lastName": "Patient",
        "dateOfBirth": "1990-01-01",
        "gender": "Male",  # Correct format: Male (not MALE)
        "age": 30,
        "identifierType": "ABHA",
        "identifierNumber": f"99-8888-7777-{str(uuid.uuid4())[:4]}",
        "mobile": "**********",
        "email": f"testfix{datetime.now().strftime('%H%M%S')}@example.com"
    }
    
    try:
        response = requests.post(url, json=patient_data, timeout=10)
        print(f"Patient Registration Test:")
        print(f"  Status Code: {response.status_code}")
        print(f"  Expected: 201")
        
        if response.status_code == 201:
            print(f"  ✅ SUCCESS: Patient registration working!")
            result = response.json()
            print(f"  Patient ID: {result.get('patientId', 'N/A')}")
            return True
        else:
            print(f"  ❌ FAILED: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ ERROR: {str(e)}")
        return False

def test_duplicate_detection_with_correct_gender():
    """Test duplicate detection with correct gender format"""
    url = "http://localhost:8080/api/patients/duplicates/check"
    
    # Test data with correct gender format
    patient_data = {
        "facilityId": "2",
        "firstName": "DuplicateTest",
        "lastName": "Patient",
        "dateOfBirth": "1985-05-15",
        "gender": "Female",  # Correct format: Female (not FEMALE)
        "age": 38,
        "identifierType": "ABHA",
        "identifierNumber": f"88-7777-6666-{str(uuid.uuid4())[:4]}",
        "mobile": "**********",
        "email": f"duptest{datetime.now().strftime('%H%M%S')}@example.com"
    }
    
    try:
        response = requests.post(url, json=patient_data, timeout=10)
        print(f"\nDuplicate Detection Test:")
        print(f"  Status Code: {response.status_code}")
        print(f"  Expected: 200")
        
        if response.status_code == 200:
            print(f"  ✅ SUCCESS: Duplicate detection working!")
            result = response.json()
            print(f"  Duplicates Found: {len(result.get('duplicates', []))}")
            return True
        else:
            print(f"  ❌ FAILED: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ ERROR: {str(e)}")
        return False

def test_upid_endpoints():
    """Test UPID endpoints that were newly created"""
    tests = [
        ("GET", "http://localhost:8080/api/upid-config/current", "UPID Current Config"),
        ("GET", "http://localhost:8080/api/upid-config/health", "UPID Health Check"),
        ("POST", "http://localhost:8080/api/upid-config/generate-sample/1", "UPID Sample Generation"),
    ]
    
    print(f"\nUPID Endpoints Test:")
    success_count = 0
    
    for method, url, name in tests:
        try:
            if method == "GET":
                response = requests.get(url, timeout=10)
            else:
                response = requests.post(url, timeout=10)
                
            print(f"  {name}: {response.status_code} {'✅' if response.status_code == 200 else '❌'}")
            if response.status_code == 200:
                success_count += 1
                
        except Exception as e:
            print(f"  {name}: ERROR - {str(e)} ❌")
    
    print(f"  UPID Success Rate: {success_count}/{len(tests)}")
    return success_count == len(tests)

def test_metrics_endpoint():
    """Test metrics endpoint"""
    url = "http://localhost:8080/q/metrics"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"\nMetrics Endpoint Test:")
        print(f"  Status Code: {response.status_code}")
        print(f"  Expected: 200")
        
        if response.status_code == 200:
            print(f"  ✅ SUCCESS: Metrics endpoint working!")
            return True
        else:
            print(f"  ❌ FAILED: Metrics not available")
            return False
            
    except Exception as e:
        print(f"  ❌ ERROR: {str(e)}")
        return False

def main():
    print("="*60)
    print("EHR Backend Quick Fix Validation")
    print("="*60)
    print(f"Testing fixes at: {datetime.now()}")
    
    # Run all tests
    results = []
    results.append(test_patient_registration_with_correct_gender())
    results.append(test_duplicate_detection_with_correct_gender())
    results.append(test_upid_endpoints())
    results.append(test_metrics_endpoint())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n" + "="*60)
    print(f"QUICK FIX VALIDATION SUMMARY")
    print(f"="*60)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print(f"🎉 ALL FIXES VALIDATED SUCCESSFULLY!")
    elif passed > total/2:
        print(f"⚠️  MOST FIXES WORKING - Some issues remain")
    else:
        print(f"❌ FIXES NEED MORE ATTENTION")
    
    return passed == total

if __name__ == "__main__":
    main()
