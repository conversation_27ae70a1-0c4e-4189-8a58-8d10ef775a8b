#!/bin/bash

# Quick Validation Script for EHR Backend Fixes
# Tests the key endpoints that were fixed

BASE_URL="${1:-http://localhost:8080}"
API_BASE="${BASE_URL}/api"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test counter
TOTAL=0
PASSED=0

test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    ((TOTAL++))
    printf "%-50s" "$name:"
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null "$url" 2>/dev/null || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✓ PASS${NC} (HTTP $response)"
        ((PASSED++))
    else
        echo -e "${RED}✗ FAIL${NC} (HTTP $response)"
    fi
}

echo "=========================================="
echo "EHR Backend Quick Validation"
echo "=========================================="
echo "Base URL: $BASE_URL"
echo "Testing key fixes..."
echo

# Test 1: Health Checks (should still work)
echo "1. Health System:"
test_endpoint "Application Health" "$BASE_URL/q/health"
test_endpoint "OpenAPI Spec" "$BASE_URL/q/openapi"

echo

# Test 2: NEW - UPID Configuration (main fix)
echo "2. UPID Configuration (NEW):"
test_endpoint "Current UPID Config" "$API_BASE/upid-config/current"
test_endpoint "Available Formats" "$API_BASE/upid-config/formats"
test_endpoint "UPID Health Check" "$API_BASE/upid-config/health"
test_endpoint "Generate Sample UPID" "$API_BASE/upid-config/generate-sample/1"

echo

# Test 3: Patient APIs (should now work)
echo "3. Patient Registration APIs:"
test_endpoint "List Patients" "$API_BASE/patients"
test_endpoint "Patient Query" "$API_BASE/patients/query?page=0&size=5"

echo

# Test 4: Master Data APIs (should now work)
echo "4. Master Data APIs:"
test_endpoint "Lookup Values" "$API_BASE/lookup-values"
test_endpoint "Countries" "$API_BASE/geo/countries"
test_endpoint "Facility Suggestions" "$API_BASE/facilities/suggest?name=hospital"

echo

# Test 5: Duplicate Detection (should still work)
echo "5. Duplicate Detection:"
test_endpoint "Statistics" "$API_BASE/patients/duplicates/statistics?days=30"
test_endpoint "Admin Config" "$API_BASE/admin/duplicate-detection/config"

echo

# Test 6: Metrics (should now work)
echo "6. Monitoring:"
test_endpoint "Metrics Endpoint" "$BASE_URL/q/metrics"

echo
echo "=========================================="
echo "VALIDATION SUMMARY"
echo "=========================================="
echo "Total Tests: $TOTAL"
echo -e "Passed: ${GREEN}$PASSED${NC}"
echo -e "Failed: ${RED}$((TOTAL - PASSED))${NC}"

if [ $PASSED -eq $TOTAL ]; then
    echo -e "\n${GREEN}🎉 ALL FIXES VALIDATED SUCCESSFULLY!${NC}"
    echo "Your EHR backend is now fully functional."
elif [ $PASSED -gt $((TOTAL / 2)) ]; then
    echo -e "\n${YELLOW}⚠ MOST FIXES WORKING${NC}"
    echo "Success rate: $(( (PASSED * 100) / TOTAL ))%"
else
    echo -e "\n${RED}❌ FIXES NEED ATTENTION${NC}"
    echo "Success rate: $(( (PASSED * 100) / TOTAL ))%"
fi

echo
echo "Next steps:"
echo "1. If all tests pass: Run full test suite with 'python test-scripts/advanced-api-test.py'"
echo "2. If some fail: Check service logs and ensure database is connected"
echo "3. For detailed testing: Run './test-scripts/run-all-tests.sh'"
