#!/bin/bash

# Master Test Runner for EHR Backend Services
# Executes all test scripts and provides comprehensive reporting

set -e

# Configuration
BASE_URL="${1:-http://localhost:8080}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLEANUP="${2:-false}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# Test tracking
TOTAL_SUITES=0
PASSED_SUITES=0
FAILED_SUITES=0
START_TIME=$(date +%s)

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✓${NC} $1"
}

error() {
    echo -e "${RED}✗${NC} $1"
}

warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

header() {
    echo
    echo -e "${CYAN}${BOLD}$1${NC}"
    echo -e "${CYAN}$(printf '=%.0s' $(seq 1 ${#1}))${NC}"
}

# Function to run a test suite
run_test_suite() {
    local suite_name="$1"
    local script_path="$2"
    local args="$3"
    
    ((TOTAL_SUITES++))
    
    header "Running $suite_name"
    log "Script: $script_path"
    log "Arguments: $args"
    
    if [ ! -f "$script_path" ]; then
        error "Test script not found: $script_path"
        ((FAILED_SUITES++))
        return 1
    fi
    
    # Make script executable
    chmod +x "$script_path"
    
    # Run the test suite
    local start_time=$(date +%s)
    
    if eval "$script_path $args"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        success "$suite_name completed successfully (${duration}s)"
        ((PASSED_SUITES++))
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        error "$suite_name failed (${duration}s)"
        ((FAILED_SUITES++))
        return 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    header "Checking Prerequisites"
    
    # Check if jq is available
    if ! command -v jq &> /dev/null; then
        warning "jq is not installed. JSON formatting will be limited."
    else
        success "jq is available"
    fi
    
    # Check if bc is available
    if ! command -v bc &> /dev/null; then
        warning "bc is not installed. Performance calculations may be limited."
    else
        success "bc is available"
    fi
    
    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        error "curl is required but not installed"
        exit 1
    else
        success "curl is available"
    fi
    
    # Check if the service is running
    log "Checking if EHR backend service is running..."
    if curl -s --connect-timeout 5 "$BASE_URL/q/health" > /dev/null 2>&1; then
        success "EHR backend service is running at $BASE_URL"
    else
        error "EHR backend service is not running at $BASE_URL"
        echo "Please start the service before running tests."
        exit 1
    fi
}

# Function to generate test report
generate_report() {
    local end_time=$(date +%s)
    local total_duration=$((end_time - START_TIME))
    local minutes=$((total_duration / 60))
    local seconds=$((total_duration % 60))
    
    header "Test Execution Summary"
    
    echo "Base URL: $BASE_URL"
    echo "Test Directory: $SCRIPT_DIR"
    echo "Total Duration: ${minutes}m ${seconds}s"
    echo
    echo "Test Suites Summary:"
    echo "  Total Suites: $TOTAL_SUITES"
    echo -e "  Passed: ${GREEN}$PASSED_SUITES${NC}"
    echo -e "  Failed: ${RED}$FAILED_SUITES${NC}"
    
    if [ $FAILED_SUITES -eq 0 ]; then
        echo
        echo -e "${GREEN}${BOLD}🎉 All test suites passed successfully!${NC}"
        echo
        echo "Your EHR backend services are running correctly."
    else
        echo
        echo -e "${RED}${BOLD}❌ Some test suites failed${NC}"
        echo
        echo "Please review the output above to identify and fix issues."
    fi
    
    # Generate detailed report file
    local report_file="$SCRIPT_DIR/test-report-$(date +%Y%m%d-%H%M%S).txt"
    {
        echo "EHR Backend Test Report"
        echo "======================"
        echo "Generated: $(date)"
        echo "Base URL: $BASE_URL"
        echo "Total Duration: ${minutes}m ${seconds}s"
        echo
        echo "Test Suites Summary:"
        echo "  Total: $TOTAL_SUITES"
        echo "  Passed: $PASSED_SUITES"
        echo "  Failed: $FAILED_SUITES"
        echo
        if [ $FAILED_SUITES -eq 0 ]; then
            echo "Result: ALL TESTS PASSED ✓"
        else
            echo "Result: SOME TESTS FAILED ✗"
        fi
    } > "$report_file"
    
    log "Detailed report saved to: $report_file"
}

# Main execution
main() {
    echo "=========================================="
    echo "EHR Backend Services - Master Test Runner"
    echo "=========================================="
    echo "Base URL: $BASE_URL"
    echo "Cleanup Mode: $CLEANUP"
    echo "Started: $(date)"
    echo
    
    # Check prerequisites
    check_prerequisites
    
    # Define test suites to run
    local test_suites=(
        "Health Check|$SCRIPT_DIR/health-check.sh|$BASE_URL"
        "Comprehensive API Test|$SCRIPT_DIR/comprehensive-api-test.sh|$BASE_URL"
        "Patient API Test|$SCRIPT_DIR/patient-api-test.sh|$BASE_URL $([ "$CLEANUP" = "true" ] && echo "--cleanup")"
        "UPID Configuration Test|$SCRIPT_DIR/upid-config-test.sh|$BASE_URL"
        "Duplicate Detection Test|$SCRIPT_DIR/duplicate-detection-test.sh|$BASE_URL"
    )
    
    # Run each test suite
    for suite_info in "${test_suites[@]}"; do
        IFS='|' read -r suite_name script_path args <<< "$suite_info"
        
        if ! run_test_suite "$suite_name" "$script_path" "$args"; then
            warning "Continuing with remaining test suites..."
        fi
        
        # Add a small delay between test suites
        sleep 2
    done
    
    # Run Python advanced tests if Python is available
    if command -v python3 &> /dev/null; then
        if [ -f "$SCRIPT_DIR/advanced-api-test.py" ]; then
            run_test_suite "Advanced API Test (Python)" "python3 $SCRIPT_DIR/advanced-api-test.py" "$BASE_URL"
        fi
    else
        warning "Python3 not available. Skipping advanced API tests."
    fi
    
    # Generate final report
    generate_report
    
    # Exit with appropriate code
    if [ $FAILED_SUITES -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Help function
show_help() {
    echo "EHR Backend Services - Master Test Runner"
    echo
    echo "Usage: $0 [BASE_URL] [CLEANUP]"
    echo
    echo "Arguments:"
    echo "  BASE_URL    Base URL of the EHR backend service (default: http://localhost:8080)"
    echo "  CLEANUP     Set to 'true' to enable cleanup mode (default: false)"
    echo
    echo "Examples:"
    echo "  $0                                    # Test localhost with default settings"
    echo "  $0 http://localhost:8080              # Test specific URL"
    echo "  $0 http://localhost:8080 true         # Test with cleanup enabled"
    echo "  $0 https://ehr-api.example.com        # Test remote server"
    echo
    echo "Test Suites Included:"
    echo "  - Health Check"
    echo "  - Comprehensive API Test"
    echo "  - Patient API Test"
    echo "  - UPID Configuration Test"
    echo "  - Duplicate Detection Test"
    echo "  - Advanced API Test (Python, if available)"
    echo
    echo "Prerequisites:"
    echo "  - curl (required)"
    echo "  - jq (recommended for JSON formatting)"
    echo "  - bc (recommended for performance calculations)"
    echo "  - python3 (optional for advanced tests)"
}

# Check for help flag
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# Run main function
main
