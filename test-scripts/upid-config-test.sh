#!/bin/bash

# UPID Configuration API Test Script
# Comprehensive testing of UPID configuration and generation APIs

set -e

BASE_URL="${1:-http://localhost:8080}"
API_BASE="${BASE_URL}/api"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✓${NC} $1"
}

error() {
    echo -e "${RED}✗${NC} $1"
}

warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Test helper function
test_api() {
    local method="$1"
    local endpoint="$2"
    local expected_status="$3"
    local data="$4"
    local description="$5"
    
    log "Testing: $description"
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    curl_cmd="$curl_cmd '$API_BASE$endpoint'"
    
    local response=$(eval "$curl_cmd")
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        success "$description (HTTP $status_code)"
        if [ -n "$body" ] && [ "$body" != "null" ]; then
            echo "$body" | jq . 2>/dev/null || echo "Response: $body"
        fi
        echo
        return 0
    else
        error "$description (Expected $expected_status, got $status_code)"
        echo "Response: $body"
        echo
        return 1
    fi
}

echo "=========================================="
echo "UPID Configuration API Test Suite"
echo "=========================================="
echo "Base URL: $BASE_URL"
echo "Testing started at: $(date)"
echo

# 1. Basic Configuration Tests
log "=== BASIC CONFIGURATION TESTS ==="

test_api "GET" "/upid-config/current" "200" "" "Get current UPID configuration"

test_api "GET" "/upid-config/formats" "200" "" "Get available UPID formats"

test_api "GET" "/upid-config/health" "200" "" "UPID configuration health check"

# 2. UPID Generation Tests
log "=== UPID GENERATION TESTS ==="

test_api "POST" "/upid-config/generate-sample/1" "200" "" "Generate sample UPID for facility 1"

test_api "POST" "/upid-config/generate-sample/2" "200" "" "Generate sample UPID for facility 2"

test_api "POST" "/upid-config/generate-sample/999" "200" "" "Generate sample UPID for facility 999"

# 3. UPID Validation Tests
log "=== UPID VALIDATION TESTS ==="

# Valid UPID formats to test
VALID_UPIDS=(
    "001-00-0000-0001"
    "002-00-0000-0001"
    "123-00-0000-9999"
)

for upid in "${VALID_UPIDS[@]}"; do
    VALIDATION_DATA='{"upid":"'$upid'"}'
    test_api "POST" "/upid-config/validate" "200" "$VALIDATION_DATA" "Validate UPID: $upid"
done

# Invalid UPID formats to test
INVALID_UPIDS=(
    "invalid-upid"
    "123"
    "001-00-0000"
    "001-00-0000-0001-extra"
    ""
)

for upid in "${INVALID_UPIDS[@]}"; do
    VALIDATION_DATA='{"upid":"'$upid'"}'
    test_api "POST" "/upid-config/validate" "200" "$VALIDATION_DATA" "Validate invalid UPID: $upid"
done

# 4. Admin Configuration Tests
log "=== ADMIN CONFIGURATION TESTS ==="

test_api "GET" "/upid-config/admin/effective" "200" "" "Get effective configuration (Admin)"

test_api "GET" "/upid-config/admin/history" "200" "" "Get configuration history"

# Test configuration validation (without applying)
VALIDATION_CONFIG='{
    "changes": {
        "upid.format.standard.facility-digits": "3",
        "upid.format.standard.separator": "-"
    },
    "validateOnly": true
}'

test_api "POST" "/upid-config/admin/validate" "200" "$VALIDATION_CONFIG" "Validate configuration changes"

# 5. Test Sample Generation with Custom Config
log "=== CUSTOM CONFIGURATION TESTS ==="

CUSTOM_CONFIG='{
    "facilityId": "1",
    "sampleCount": 3,
    "tempConfig": {
        "upid.format.type": "STANDARD",
        "upid.format.standard.facility-digits": "3"
    }
}'

test_api "POST" "/upid-config/admin/test-samples/1" "200" "$CUSTOM_CONFIG" "Generate test samples with custom config"

# 6. Error Handling Tests
log "=== ERROR HANDLING TESTS ==="

# Test with missing UPID in validation
EMPTY_VALIDATION='{"upid":""}'
test_api "POST" "/upid-config/validate" "200" "$EMPTY_VALIDATION" "Validate empty UPID"

# Test with malformed JSON
test_api "POST" "/upid-config/validate" "400" '{"invalid":json}' "Malformed JSON validation"

# Test invalid facility ID for sample generation
test_api "POST" "/upid-config/generate-sample/invalid" "400" "" "Invalid facility ID for sample generation"

# 7. Performance Tests
log "=== PERFORMANCE TESTS ==="

log "Testing UPID generation performance..."
start_time=$(date +%s.%N)
for i in {1..10}; do
    curl -s -X POST "$API_BASE/upid-config/generate-sample/1" > /dev/null
done
end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc -l)
avg_duration=$(echo "$duration / 10" | bc -l)
avg_duration_ms=$(echo "$avg_duration * 1000" | bc -l | cut -d. -f1)

if (( avg_duration_ms < 100 )); then
    success "UPID generation performance: ${avg_duration_ms}ms average (< 100ms)"
else
    warning "UPID generation performance: ${avg_duration_ms}ms average (>= 100ms)"
fi

# 8. Configuration Consistency Tests
log "=== CONFIGURATION CONSISTENCY TESTS ==="

log "Checking configuration consistency..."

# Get current config
CURRENT_CONFIG=$(curl -s "$API_BASE/upid-config/current")
FORMAT_TYPE=$(echo "$CURRENT_CONFIG" | jq -r '.formatType' 2>/dev/null || echo "unknown")

log "Current format type: $FORMAT_TYPE"

# Generate sample and validate it
SAMPLE_RESPONSE=$(curl -s -X POST "$API_BASE/upid-config/generate-sample/1")
SAMPLE_UPID=$(echo "$SAMPLE_RESPONSE" | jq -r '.upid' 2>/dev/null || echo "")

if [ -n "$SAMPLE_UPID" ] && [ "$SAMPLE_UPID" != "null" ]; then
    log "Generated sample UPID: $SAMPLE_UPID"
    
    # Validate the generated UPID
    VALIDATION_DATA='{"upid":"'$SAMPLE_UPID'"}'
    VALIDATION_RESPONSE=$(curl -s -X POST -H 'Content-Type: application/json' -d "$VALIDATION_DATA" "$API_BASE/upid-config/validate")
    IS_VALID=$(echo "$VALIDATION_RESPONSE" | jq -r '.valid' 2>/dev/null || echo "false")
    
    if [ "$IS_VALID" = "true" ]; then
        success "Generated UPID passes validation"
    else
        error "Generated UPID fails validation"
        echo "Validation response: $VALIDATION_RESPONSE"
    fi
else
    error "Failed to generate sample UPID"
fi

# 9. Format Information Tests
log "=== FORMAT INFORMATION TESTS ==="

log "Testing format information retrieval..."
FORMATS_RESPONSE=$(curl -s "$API_BASE/upid-config/formats")
FORMAT_COUNT=$(echo "$FORMATS_RESPONSE" | jq '. | length' 2>/dev/null || echo "0")

if [ "$FORMAT_COUNT" -gt "0" ]; then
    success "Retrieved $FORMAT_COUNT UPID format(s)"
    echo "$FORMATS_RESPONSE" | jq '.[] | {type: .type, description: .description, example: .example}' 2>/dev/null || echo "Format details not available"
else
    error "No UPID formats retrieved"
fi

echo
echo "=========================================="
echo "UPID Configuration Test Summary"
echo "=========================================="
echo "All UPID configuration API tests completed."
echo "Check the output above for any failures."
echo
echo "Key findings:"
echo "- Current format type: $FORMAT_TYPE"
echo "- Available formats: $FORMAT_COUNT"
if [ -n "$SAMPLE_UPID" ]; then
    echo "- Sample UPID: $SAMPLE_UPID"
fi
