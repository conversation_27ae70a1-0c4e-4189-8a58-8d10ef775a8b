#!/usr/bin/env python3
"""
Test script to verify the transaction fix for patient registration.
This script tests the patient registration API to ensure the transaction error is resolved.
"""

import requests
import json
import time
import sys
from datetime import datetime, date

# Configuration
BASE_URL = "http://localhost:8080/api"
PATIENT_ENDPOINT = f"{BASE_URL}/patients"

def create_test_patient_data():
    """Create test patient data for registration"""
    timestamp = int(time.time())
    
    return {
        "firstName": f"TestPatient{timestamp}",
        "lastName": "TransactionTest",
        "middleName": "Fix",
        "dateOfBirth": "1990-01-15",
        "gender": "MALE",
        "facilityId": "2",
        "age": 30,
        "identifierType": "AADHAAR",
        "identifierNumber": f"**********{timestamp % 100:02d}",
        "contacts": [
            {
                "phoneNumber": f"9876543{timestamp % 1000:03d}",
                "email": f"test{timestamp}@example.com",
                "contactType": "PRIMARY"
            }
        ],
        "addresses": [
            {
                "addressLine1": "123 Test Street",
                "cityOrVillage": "TestCity",
                "stateOrProvince": "TestState",
                "postalCode": "123456",
                "country": "India",
                "addressType": "PERMANENT"
            }
        ]
    }

def test_patient_registration():
    """Test patient registration to verify transaction fix"""
    print("🧪 Testing Patient Registration Transaction Fix")
    print("=" * 50)
    
    # Create test data
    patient_data = create_test_patient_data()
    
    print(f"📝 Test Patient Data:")
    print(f"   Name: {patient_data['firstName']} {patient_data['lastName']}")
    print(f"   Facility ID: {patient_data['facilityId']}")
    print(f"   Phone: {patient_data['contacts'][0]['phoneNumber']}")
    print()
    
    try:
        print("🚀 Sending registration request...")
        
        # Send registration request
        response = requests.post(
            PATIENT_ENDPOINT,
            json=patient_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 201:
            # Success
            response_data = response.json()
            patient_id = response_data.get('patientId', 'Unknown')
            
            print("✅ SUCCESS: Patient registration completed successfully!")
            print(f"   Patient ID: {patient_id}")
            print(f"   Registration Date: {response_data.get('registrationDate', 'Unknown')}")
            
            # Verify the patient was created
            print("\n🔍 Verifying patient creation...")
            verify_response = requests.get(f"{PATIENT_ENDPOINT}/{patient_id}")
            
            if verify_response.status_code == 200:
                print("✅ Patient verification successful!")
                return True
            else:
                print(f"⚠️  Patient verification failed: {verify_response.status_code}")
                return False
                
        elif response.status_code == 400:
            # Bad request - check if it's a validation error or transaction error
            try:
                error_data = response.json()
                error_message = error_data.get('error', str(error_data))
                
                if 'transaction' in error_message.lower() or 'enlisted connection' in error_message.lower():
                    print("❌ TRANSACTION ERROR STILL EXISTS!")
                    print(f"   Error: {error_message}")
                    return False
                else:
                    print(f"⚠️  Validation Error (Expected): {error_message}")
                    return True  # Validation errors are expected and OK
                    
            except json.JSONDecodeError:
                print(f"❌ Bad Request: {response.text}")
                return False
                
        elif response.status_code == 500:
            # Internal server error - likely the transaction issue
            try:
                error_data = response.json()
                error_message = error_data.get('error', str(error_data))
            except:
                error_message = response.text
                
            print("❌ INTERNAL SERVER ERROR!")
            print(f"   Error: {error_message}")
            
            if 'transaction' in error_message.lower() or 'enlisted connection' in error_message.lower():
                print("   🔍 This appears to be the transaction error we're trying to fix!")
                return False
            else:
                print("   🔍 This appears to be a different error.")
                return False
                
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION ERROR: Could not connect to the server.")
        print("   Make sure the EHR backend server is running on localhost:8080")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ TIMEOUT ERROR: Request timed out after 30 seconds.")
        print("   This might indicate a transaction deadlock or similar issue.")
        return False
        
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {str(e)}")
        return False

def check_server_health():
    """Check if the server is running and healthy"""
    try:
        health_response = requests.get(f"{BASE_URL}/q/health", timeout=5)
        return health_response.status_code == 200
    except:
        return False

def main():
    """Main test function"""
    print("🏥 EHR Patient Registration Transaction Fix Test")
    print("=" * 60)
    print(f"🕒 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check server health first
    print("🔍 Checking server health...")
    if not check_server_health():
        print("❌ Server is not running or not healthy!")
        print("   Please start the EHR backend server first.")
        sys.exit(1)
    
    print("✅ Server is running and healthy!")
    print()
    
    # Run the test
    success = test_patient_registration()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 TEST PASSED: Transaction fix appears to be working!")
        print("   Patient registration completed without transaction errors.")
    else:
        print("💥 TEST FAILED: Transaction error still exists or other issues found.")
        print("   Please check the server logs for more details.")
    
    print(f"🕒 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
